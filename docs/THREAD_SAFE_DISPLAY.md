# Thread-Safe Display System

This document describes the thread-safe display abstraction system that allows multiple threads to independently manage different portions of the display without interfering with each other.

## Overview

The system consists of three main components:

1. **`DisplayRegion`** - Represents a bounded area of the display that can be safely accessed by a single thread
2. **`ThreadSafeDisplay`** - Manages the main LGFX instance and coordinates access between regions
3. **`DisplayRegionManager`** - Handles region allocation and prevents overlapping regions

## Key Features

- **Thread Safety**: Multiple threads can draw to different regions simultaneously
- **Automatic Clipping**: All drawing operations are automatically clipped to region boundaries
- **Overlap Prevention**: The system prevents allocation of overlapping regions
- **Resource Management**: Automatic cleanup of regions when released
- **Familiar API**: DisplayRegion provides a familiar drawing API similar to LGFX

## Basic Usage

### 1. Initialize the Display

```cpp
#include "display.h"

Display* display = new Display();
display->setup();
```

### 2. Create Display Regions

```cpp
// Create a region in the top-left corner (200x100 pixels)
DisplayRegion* myRegion = display->createRegion(0, 0, 200, 100);

if (myRegion) {
    // Region created successfully
    myRegion->clear(TFT_BLUE);
    myRegion->setCursor(10, 10);
    myRegion->setTextColor(TFT_WHITE);
    myRegion->print("Hello World!");
    
    // Release the region when done
    display->releaseRegion(myRegion);
}
```

### 3. Multi-threaded Usage

```cpp
void taskFunction(void* parameter) {
    // Each task gets its own region
    DisplayRegion* region = display->createRegion(x, y, width, height);
    
    if (region) {
        while (true) {
            // Update the region content
            region->clear(TFT_BLACK);
            region->setCursor(10, 10);
            region->printf("Time: %lu", millis());
            
            vTaskDelay(pdMS_TO_TICKS(1000));
        }
        
        // Clean up (if task ends)
        display->releaseRegion(region);
    }
    
    vTaskDelete(NULL);
}

// Create the task
xTaskCreate(taskFunction, "DisplayTask", 4096, NULL, 1, NULL);
```

## DisplayRegion API

### Drawing Operations

All coordinates are relative to the region's top-left corner:

```cpp
// Basic shapes
region->fillRect(x, y, width, height, color);
region->drawRect(x, y, width, height, color);
region->fillCircle(x, y, radius, color);
region->drawCircle(x, y, radius, color);
region->drawLine(x0, y0, x1, y1, color);
region->drawPixel(x, y, color);

// Text operations
region->setCursor(x, y);
region->setTextColor(color);
region->setTextColor(fgColor, bgColor);
region->setTextSize(size);
region->print("text");
region->printf("Value: %d", value);

// Region management
region->clear(color);           // Clear entire region
region->refresh();              // Force refresh (if supported)
```

### Region Information

```cpp
int32_t x = region->getX();           // Absolute X position
int32_t y = region->getY();           // Absolute Y position
int32_t w = region->getWidth();       // Region width
int32_t h = region->getHeight();      // Region height
uint8_t id = region->getId();         // Unique region ID

bool contains = region->contains(x, y); // Check if point is in region
```

## Thread Safety Considerations

### Safe Operations
- All `DisplayRegion` methods are thread-safe
- Multiple threads can draw to different regions simultaneously
- Region creation and destruction are thread-safe

### Unsafe Operations
- Direct access to `display->getGfx()` is NOT thread-safe
- Sharing a single `DisplayRegion` between multiple threads is NOT safe
- Modifying display-wide settings (brightness, rotation) should be done carefully

### Best Practices

1. **One Region Per Thread**: Each thread should have its own dedicated region
2. **Proper Cleanup**: Always release regions when done
3. **Error Handling**: Check if region creation succeeds before using
4. **Avoid Direct LGFX Access**: Use DisplayRegion methods instead of direct LGFX calls

## Example: Multi-threaded Dashboard

```cpp
// Task 1: Clock display (top-left)
void clockTask(void* param) {
    DisplayRegion* region = display->createRegion(0, 0, 240, 60);
    while (true) {
        region->clear(TFT_NAVY);
        region->setCursor(10, 20);
        region->setTextColor(TFT_WHITE);
        region->printf("%02d:%02d:%02d", hours, minutes, seconds);
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}

// Task 2: Status display (top-right)
void statusTask(void* param) {
    DisplayRegion* region = display->createRegion(240, 0, 240, 60);
    while (true) {
        region->clear(TFT_DARKGREEN);
        region->setCursor(10, 15);
        region->setTextColor(TFT_WHITE);
        region->printf("Heap: %d", ESP.getFreeHeap());
        vTaskDelay(pdMS_TO_TICKS(2000));
    }
}

// Task 3: Graph display (bottom)
void graphTask(void* param) {
    DisplayRegion* region = display->createRegion(0, 60, 480, 420);
    while (true) {
        // Draw animated graph
        updateGraph(region);
        vTaskDelay(pdMS_TO_TICKS(100));
    }
}
```

## Error Handling

```cpp
DisplayRegion* region = display->createRegion(x, y, width, height);
if (!region) {
    Serial.println("Failed to create region - possible causes:");
    Serial.println("- Region overlaps with existing region");
    Serial.println("- Region extends beyond display boundaries");
    Serial.println("- Out of memory");
    return;
}

// Use region...

display->releaseRegion(region);
```

## Performance Considerations

- Region operations use mutexes for thread safety, which adds small overhead
- Frequent region creation/destruction should be avoided
- Large regions with complex graphics may impact performance
- Consider using lower priority for graphics-intensive tasks

## Migration from Direct LGFX Usage

### Before (Not Thread-Safe)
```cpp
LGFX& gfx = display->getGfx();
gfx.fillRect(x, y, w, h, color);
gfx.setCursor(x, y);
gfx.print("text");
```

### After (Thread-Safe)
```cpp
DisplayRegion* region = display->createRegion(x, y, w, h);
region->fillRect(0, 0, w, h, color);  // Note: relative coordinates
region->setCursor(0, 0);
region->print("text");
display->releaseRegion(region);
```

## See Also

- `examples/multi-thread-display-example.cpp` - Complete working example
- `include/display-region.h` - DisplayRegion API reference
- `include/thread-safe-display.h` - ThreadSafeDisplay API reference
