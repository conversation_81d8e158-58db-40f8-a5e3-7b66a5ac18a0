# Color Troubleshooting Guide

This guide helps diagnose and fix color display issues with the ESP32 RGB display system.

## Common Color Issues

### 1. **Colors Appear Wrong/Swapped**

**Symptoms:**
- Red appears blue or green
- Colors are shifted or inverted
- Display shows unexpected color combinations

**Possible Causes:**
- RGB channel order mismatch
- Incorrect color format
- Hardware wiring issues

**Solutions:**

#### Check RGB Channel Order
The ESP32-4848S040 uses this pin mapping:
- **Blue (B0-B4)**: GPIO 8, 3, 46, 9, 1
- **Green (G0-G5)**: GPIO 5, 6, 7, 15, 16, 4  
- **Red (R0-R4)**: GPIO 45, 48, 47, 21, 14

If colors are swapped, check your hardware configuration in `include/boards/esp32-4848s040.h`.

#### Test with Known Colors
Use the color test utility to verify colors:

```cpp
// Copy examples/color-test.cpp to src/main.cpp temporarily
// Upload and check if colors match expectations:
// - Pure red should be bright red (0xF800)
// - Pure green should be bright green (0x07E0)  
// - Pure blue should be bright blue (0x001F)
```

### 2. **Colors Appear Dim or Washed Out**

**Symptoms:**
- All colors appear darker than expected
- Poor contrast
- Colors lack saturation

**Possible Causes:**
- Low backlight brightness
- Incorrect voltage levels
- Display panel issues

**Solutions:**

#### Adjust Backlight Brightness
```cpp
display->setBrightness(255); // Maximum brightness (0-255)
```

#### Check Power Supply
Ensure your power supply can provide sufficient current for the display.

### 3. **Specific Color Channels Missing**

**Symptoms:**
- Only red/green/blue colors visible
- One or more color channels completely missing
- Monochrome display

**Possible Causes:**
- Broken RGB data lines
- Incorrect pin configuration
- Hardware damage

**Solutions:**

#### Test Individual Channels
```cpp
// Test pure colors individually
const uint16_t TEST_RED = 0xF800;    // Should show pure red
const uint16_t TEST_GREEN = 0x07E0;  // Should show pure green  
const uint16_t TEST_BLUE = 0x001F;   // Should show pure blue

DisplayRegion* region = display->createRegion(0, 0, 160, 160);
region->clear(TEST_RED);   // Test red channel
delay(2000);
region->clear(TEST_GREEN); // Test green channel  
delay(2000);
region->clear(TEST_BLUE);  // Test blue channel
```

## Color Format Reference

### RGB565 Format
The display uses 16-bit RGB565 format:
- **5 bits Red** (bits 15-11): Values 0-31
- **6 bits Green** (bits 10-5): Values 0-63
- **5 bits Blue** (bits 4-0): Values 0-31

### Color Conversion
To convert 8-bit RGB to RGB565:
```cpp
uint16_t rgb888_to_rgb565(uint8_t r, uint8_t g, uint8_t b) {
    return ((r >> 3) << 11) | ((g >> 2) << 5) | (b >> 3);
}
```

### Common RGB565 Colors
```cpp
const uint16_t COLOR_BLACK   = 0x0000;  // 00000 000000 00000
const uint16_t COLOR_WHITE   = 0xFFFF;  // 11111 111111 11111
const uint16_t COLOR_RED     = 0xF800;  // 11111 000000 00000
const uint16_t COLOR_GREEN   = 0x07E0;  // 00000 111111 00000
const uint16_t COLOR_BLUE    = 0x001F;  // 00000 000000 11111
const uint16_t COLOR_YELLOW  = 0xFFE0;  // 11111 111111 00000
const uint16_t COLOR_CYAN    = 0x07FF;  // 00000 111111 11111
const uint16_t COLOR_MAGENTA = 0xF81F;  // 11111 000000 11111
```

## Diagnostic Steps

### Step 1: Basic Color Test
1. Upload the color test example (`examples/color-test.cpp`)
2. Observe the display output
3. Compare with expected colors

### Step 2: Check Hardware Connections
1. Verify all RGB data pins are connected correctly
2. Check power supply voltage (3.3V for logic, appropriate voltage for backlight)
3. Ensure ground connections are solid

### Step 3: Test Individual Components
```cpp
// Test backlight
display->setBrightness(0);   // Should be dark
display->setBrightness(255); // Should be bright

// Test display initialization
bool init_ok = display->setup();
Serial.printf("Display init: %s\n", init_ok ? "OK" : "FAILED");
```

### Step 4: Check Color Depth Settings
The LGFX library should automatically use RGB565 for RGB panels, but you can verify:
```cpp
// In your LGFX configuration, ensure:
// _write_depth = color_depth_t::rgb565_2Byte;
// _read_depth = color_depth_t::rgb565_2Byte;
```

## Advanced Troubleshooting

### Enable Debug Output
Add debug information to see what's happening:
```cpp
void debugColorTest() {
    const uint16_t test_color = 0xF800; // Red
    
    Serial.printf("Testing color: 0x%04X\n", test_color);
    Serial.printf("Red component: %d\n", (test_color >> 11) & 0x1F);
    Serial.printf("Green component: %d\n", (test_color >> 5) & 0x3F);
    Serial.printf("Blue component: %d\n", test_color & 0x1F);
    
    DisplayRegion* region = display->createRegion(0, 0, 100, 100);
    region->clear(test_color);
    display->releaseRegion(region);
}
```

### Check LGFX Configuration
Verify your LGFX setup matches your hardware:
```cpp
// In your LGFX class constructor, verify:
cfg.memory_width  = 480;  // Should match your display
cfg.memory_height = 480;  // Should match your display
cfg.panel_width   = 480;  // Should match your display  
cfg.panel_height  = 480;  // Should match your display
```

## Getting Help

If colors are still incorrect after following this guide:

1. **Document the Issue:**
   - Take photos of the actual display output
   - Note which colors appear wrong
   - List what you've already tried

2. **Provide System Information:**
   - ESP32 board model
   - Display panel model
   - Power supply specifications
   - Wiring diagram

3. **Test Results:**
   - Output from the color test utility
   - Serial monitor output with debug information
   - Voltage measurements if possible

## Quick Fixes

### Most Common Solutions:
1. **Adjust brightness:** `display->setBrightness(255);`
2. **Check power supply:** Ensure adequate current capacity
3. **Verify wiring:** Double-check RGB data pin connections
4. **Test with known good colors:** Use the provided color constants

### Emergency Color Mapping:
If colors are consistently swapped, you can create a color mapping function:
```cpp
uint16_t fixColor(uint16_t color) {
    // Example: if red and blue are swapped
    uint16_t r = (color >> 11) & 0x1F;
    uint16_t g = (color >> 5) & 0x3F;
    uint16_t b = color & 0x1F;
    
    // Swap red and blue
    return (b << 11) | (g << 5) | r;
}
```

This should help you identify and resolve most color-related issues with your ESP32 RGB display system.
