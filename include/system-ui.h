#ifndef SYSTEM_UI_H
#define SYSTEM_UI_H

#include "display.h"

/**
 * Handles system-level UI elements
 */
class SystemUI
{
public:
    /**
     * Constructor
     * @param displayDriver Pointer to the display driver
     */
    SystemUI(Display *display);

    /**
     * Initialize the system UI
     */
    void setup();

    /**
     * Update the system UI
     */
    void loop();

private:
    Display *display;
    unsigned long lastUpdateTime;

    /**
     * Update the signal strength indicator
     */
    void updateSignalStrength();
};

#endif // SYSTEM_UI_H
