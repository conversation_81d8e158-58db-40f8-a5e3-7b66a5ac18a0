#ifndef SYSTEM_UI_H
#define SYSTEM_UI_H

#include "display.h"
#include "display-region.h"

/**
 * Handles system-level UI elements using a dedicated display region
 */
class SystemUI
{
public:
    /**
     * Constructor
     * @param display Pointer to the display driver
     */
    SystemUI(Display *display);

    /**
     * Destructor
     */
    ~SystemUI();

    /**
     * Initialize the system UI
     */
    void setup();

    /**
     * Update the system UI
     */
    void loop();

private:
    Display *display;
    DisplayRegion *systemRegion;
    unsigned long lastUpdateTime;

    /**
     * Update the signal strength indicator
     */
    void updateSignalStrength();
};

#endif // SYSTEM_UI_H
