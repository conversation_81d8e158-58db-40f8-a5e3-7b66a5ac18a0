#ifndef DISPLAY_H
#define DISPLAY_H

#include "hardware.h"
#include "thread-safe-display.h"

class Display
{
    ThreadSafeDisplay threadSafeDisplay;

public:
    Display() : threadSafeDisplay(LCD_WIDTH, LCD_HEIGHT) {}

    bool setup()
    {
        bool result = threadSafeDisplay.setup();

        if (result) {
            // Create demo regions to show the concept
            createDemoRegions();
        }

        return result;
    }

    /**
     * Create a display region for exclusive use by a thread
     * @param x Left boundary of the region
     * @param y Top boundary of the region
     * @param width Width of the region
     * @param height Height of the region
     * @return Pointer to DisplayRegion (nullptr if allocation failed)
     */
    DisplayRegion* createRegion(int32_t x, int32_t y, int32_t width, int32_t height)
    {
        return threadSafeDisplay.createRegion(x, y, width, height);
    }

    /**
     * Release a display region
     * @param region Pointer to the region to release
     */
    void releaseRegion(DisplayRegion* region)
    {
        threadSafeDisplay.releaseRegion(region);
    }

    /**
     * Get the underlying LGFX instance (use with caution - not thread safe)
     * This is kept for backward compatibility but should be avoided in multi-threaded code
     */
    LGFX& getGfx() { return threadSafeDisplay.getGfx(); }

    /**
     * Get the thread-safe display instance
     */
    ThreadSafeDisplay& getThreadSafeDisplay() { return threadSafeDisplay; }

    /**
     * Get display dimensions
     */
    int32_t getWidth() const { return threadSafeDisplay.getWidth(); }
    int32_t getHeight() const { return threadSafeDisplay.getHeight(); }

    /**
     * Clear the entire display (thread-safe)
     */
    void clearDisplay(uint32_t color = 0x0000) { threadSafeDisplay.clearDisplay(color); }

    /**
     * Set display brightness (thread-safe)
     */
    void setBrightness(uint8_t brightness) { threadSafeDisplay.setBrightness(brightness); }

private:
    void createDemoRegions()
    {
        // Create demo regions to show the concept
        auto x_middle = LCD_WIDTH / 2;
        auto y_middle = LCD_HEIGHT / 2;
        auto x_width = LCD_WIDTH / 2;
        auto y_height = LCD_HEIGHT / 2;
        auto text_offset = 10;

        // Create regions for each quadrant
        DisplayRegion* redRegion = createRegion(0, 30, x_width, y_height - 30);
        if (redRegion) {
            redRegion->clear(TFT_RED);
            redRegion->setCursor(text_offset, text_offset);
            redRegion->setTextColor(TFT_WHITE);
            redRegion->print("RED REGION");
            releaseRegion(redRegion);
        }

        DisplayRegion* greenRegion = createRegion(x_middle, 30, x_width, y_height - 30);
        if (greenRegion) {
            greenRegion->clear(TFT_GREEN);
            greenRegion->setCursor(text_offset, text_offset);
            greenRegion->setTextColor(TFT_BLACK);
            greenRegion->print("GREEN REGION");
            releaseRegion(greenRegion);
        }

        DisplayRegion* blueRegion = createRegion(0, y_middle, x_width, y_height);
        if (blueRegion) {
            blueRegion->clear(TFT_BLUE);
            blueRegion->setCursor(text_offset, text_offset);
            blueRegion->setTextColor(TFT_WHITE);
            blueRegion->print("BLUE REGION");
            releaseRegion(blueRegion);
        }

        DisplayRegion* whiteRegion = createRegion(x_middle, y_middle, x_width, y_height);
        if (whiteRegion) {
            whiteRegion->clear(TFT_WHITE);
            whiteRegion->setCursor(text_offset, text_offset);
            whiteRegion->setTextColor(TFT_BLACK);
            whiteRegion->print("WHITE REGION");
            releaseRegion(whiteRegion);
        }
    }
};

#endif // DISPLAY_H