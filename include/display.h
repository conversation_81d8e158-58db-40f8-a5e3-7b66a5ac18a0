#ifndef DISPLAY_H
#define DISPLAY_H

#include "hardware.h"

class Display
{
    LGFX gfx;

public:
    void setup()
    {
        pinMode(PIN_LCD_BL, ANALOG);
        analogWrite(PIN_LCD_BL, 128);

        bool init_result = gfx.init();
        gfx.setRotation(0);

        auto x_middle = LCD_WIDTH / 2;
        auto y_middle = LCD_HEIGHT / 2;
        auto x_width = LCD_WIDTH / 2;
        auto y_height = LCD_HEIGHT / 2; 
        auto text_offset = 10;

        gfx.fillRect(0, 0 + 30, x_width, y_height - 30, TFT_RED);
        gfx.setCursor(text_offset, text_offset + 30);
        gfx.setTextColor(TFT_WHITE);
        gfx.print("RED");

        gfx.fillRect(x_middle, 0 + 30, x_width, y_height - 30, TFT_GREEN);
        gfx.setCursor(x_middle + text_offset, text_offset + 30);
        gfx.setTextColor(TFT_BLACK);
        gfx.print("GREEN");

        gfx.fillRect(0, y_middle, x_width, y_height, TFT_BLUE);
        gfx.setCursor(text_offset, y_middle + text_offset);
        gfx.setTextColor(TFT_WHITE);
        gfx.print("BLUE");

        gfx.fillRect(x_middle, y_middle, x_width, y_height, TFT_WHITE);
        gfx.setCursor(x_middle + text_offset, y_middle + text_offset);
        gfx.setTextColor(TFT_BLACK);
        gfx.print("WHITE");
    }

     LGFX& getGfx() { return gfx; }
};

#endif // DISPLAY_H