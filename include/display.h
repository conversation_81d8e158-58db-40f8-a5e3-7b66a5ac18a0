#ifndef DISPLAY_H
#define DISPLAY_H

#include "hardware.h"
#include "thread-safe-display.h"

class Display
{
    ThreadSafeDisplay threadSafeDisplay;

public:
    Display() : threadSafeDisplay(LCD_WIDTH, LCD_HEIGHT) {}

    bool setup()
    {
        bool result = threadSafeDisplay.setup();

        if (result) {
            // Create demo regions to show the concept
            createDemoRegions();
        }

        return result;
    }

    /**
     * Create a display region for exclusive use by a thread
     * @param x Left boundary of the region
     * @param y Top boundary of the region
     * @param width Width of the region
     * @param height Height of the region
     * @return Pointer to DisplayRegion (nullptr if allocation failed)
     */
    DisplayRegion* createRegion(int32_t x, int32_t y, int32_t width, int32_t height)
    {
        return threadSafeDisplay.createRegion(x, y, width, height);
    }

    /**
     * Release a display region
     * @param region Pointer to the region to release
     */
    void releaseRegion(DisplayRegion* region)
    {
        threadSafeDisplay.releaseRegion(region);
    }

    /**
     * Get the underlying LGFX instance (use with caution - not thread safe)
     * This is kept for backward compatibility but should be avoided in multi-threaded code
     */
    LGFX& getGfx() { return threadSafeDisplay.getGfx(); }

    /**
     * Get the thread-safe display instance
     */
    ThreadSafeDisplay& getThreadSafeDisplay() { return threadSafeDisplay; }

    /**
     * Get display dimensions
     */
    int32_t getWidth() const { return threadSafeDisplay.getWidth(); }
    int32_t getHeight() const { return threadSafeDisplay.getHeight(); }

    /**
     * Clear the entire display (thread-safe)
     */
    void clearDisplay(uint32_t color = 0x0000) { threadSafeDisplay.clearDisplay(color); }

    /**
     * Set display brightness (thread-safe)
     */
    void setBrightness(uint8_t brightness) { threadSafeDisplay.setBrightness(brightness); }

private:
    void createDemoRegions()
    {
        // Create demo regions to show the concept
        auto x_middle = LCD_WIDTH / 2;
        auto y_middle = LCD_HEIGHT / 2;
        auto x_width = LCD_WIDTH / 2;
        auto y_height = LCD_HEIGHT / 2;
        auto text_offset = 10;

        // Use explicit color values to ensure they work correctly
        // These are RGB565 format colors (5 bits red, 6 bits green, 5 bits blue)
        const uint16_t COLOR_RED = 0xF800;      // Pure red   (11111 000000 00000)
        const uint16_t COLOR_GREEN = 0x07E0;    // Pure green (00000 111111 00000)
        const uint16_t COLOR_BLUE = 0x001F;     // Pure blue  (00000 000000 11111)
        const uint16_t COLOR_WHITE = 0xFFFF;    // White      (11111 111111 11111)
        const uint16_t COLOR_BLACK = 0x0000;    // Black      (00000 000000 00000)
        const uint16_t COLOR_YELLOW = 0xFFE0;   // Yellow     (11111 111111 00000)
        const uint16_t COLOR_CYAN = 0x07FF;     // Cyan       (00000 111111 11111)
        const uint16_t COLOR_MAGENTA = 0xF81F;  // Magenta    (11111 000000 11111)

        // Create regions for each quadrant with different colors for testing
        DisplayRegion* redRegion = createRegion(0, 30, x_width, y_height - 30);
        if (redRegion) {
            redRegion->clear(COLOR_RED);
            redRegion->setCursor(text_offset, text_offset);
            redRegion->setTextColor(COLOR_WHITE);
            redRegion->setTextSize(2);
            redRegion->print("RED");
            redRegion->setCursor(text_offset, text_offset + 20);
            redRegion->setTextSize(1);
            redRegion->printf("0x%04X", COLOR_RED);
            releaseRegion(redRegion);
        }

        DisplayRegion* greenRegion = createRegion(x_middle, 30, x_width, y_height - 30);
        if (greenRegion) {
            greenRegion->clear(COLOR_GREEN);
            greenRegion->setCursor(text_offset, text_offset);
            greenRegion->setTextColor(COLOR_BLACK);
            greenRegion->setTextSize(2);
            greenRegion->print("GREEN");
            greenRegion->setCursor(text_offset, text_offset + 20);
            greenRegion->setTextSize(1);
            greenRegion->printf("0x%04X", COLOR_GREEN);
            releaseRegion(greenRegion);
        }

        DisplayRegion* blueRegion = createRegion(0, y_middle, x_width, y_height);
        if (blueRegion) {
            blueRegion->clear(COLOR_BLUE);
            blueRegion->setCursor(text_offset, text_offset);
            blueRegion->setTextColor(COLOR_WHITE);
            blueRegion->setTextSize(2);
            blueRegion->print("BLUE");
            blueRegion->setCursor(text_offset, text_offset + 20);
            blueRegion->setTextSize(1);
            blueRegion->printf("0x%04X", COLOR_BLUE);
            releaseRegion(blueRegion);
        }

        DisplayRegion* magentaRegion = createRegion(x_middle, y_middle, x_width, y_height);
        if (magentaRegion) {
            magentaRegion->clear(COLOR_MAGENTA);
            magentaRegion->setCursor(text_offset, text_offset);
            magentaRegion->setTextColor(COLOR_WHITE);
            magentaRegion->setTextSize(2);
            magentaRegion->print("MAGENTA");
            magentaRegion->setCursor(text_offset, text_offset + 20);
            magentaRegion->setTextSize(1);
            magentaRegion->printf("0x%04X", COLOR_MAGENTA);
            releaseRegion(magentaRegion);
        }
    }
};

#endif // DISPLAY_H