#ifndef CONFIG_H
#define CONFIG_H

#include <ArduinoJson.h>

class Config
{
private:
    const char *filename;
    JsonDocument config;

    void load(void);

public:
    Config(const char *filename = "/config.json");

    void save(void);

    const char *getWifiSsid(void) { 
        return this->config["wifi"]["ssid"].isNull() ? nullptr : this->config["wifi"]["ssid"].as<const char*>(); 
    }
    const char *getWifiPassword(void) { 
        return this->config["wifi"]["password"].isNull() ? nullptr : this->config["wifi"]["password"].as<const char*>(); 
    }
    const char *getWhatsappNumber(void) { 
        return this->config["whatsapp"]["number"].isNull() ? nullptr : this->config["whatsapp"]["number"].as<const char*>(); 
    }
    const char *getWhatsappApiKey(void) { 
        return this->config["whatsapp"]["apikey"].isNull() ? nullptr : this->config["whatsapp"]["apikey"].as<const char*>(); 
    }
};

#endif // CONFIG_H