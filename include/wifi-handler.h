#ifndef WIFI_HANDLER_H
#define WIFI_HANDLER_H

#include <Arduino.h>

class WifiHandler
{
private:
    TaskHandle_t threadHandle;

    static void thread(void *context);

public:
    WifiHandler();
    ~WifiHandler();

    /**
     * Initialize WiFi connection and create monitoring thread
     * @param ssid WiFi SSID
     * @param password WiFi password
     */
    void setup(const char *ssid, const char *password);
};

#endif /* WIFI_HANDLER_H */