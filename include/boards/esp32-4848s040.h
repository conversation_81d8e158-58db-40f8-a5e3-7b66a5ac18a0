#ifndef ESP32_4848S040_H
#define ESP32_4848S040_H

#include <LovyanGFX.hpp>
#include <lgfx/v1/platforms/esp32s3/Panel_RGB.hpp>
#include <lgfx/v1/platforms/esp32s3/Bus_RGB.hpp>

/*ESP32-4848S040 with GT911 touch and ST7701 display via RGB interface*/

// Display pins for RGB interface
#define PIN_LCD_DE     18  // Data Enable
#define PIN_LCD_VSYNC  17  // Vertical Sync
#define PIN_LCD_HSYNC  16  // Horizontal Sync
#define PIN_LCD_PCLK   21  // Pixel Clock

// RGB data pins
// Blue pins
#define PIN_LCD_B0     11
#define PIN_LCD_B1     12
#define PIN_LCD_B2     13
#define PIN_LCD_B3     14
#define PIN_LCD_B4     0

// Green pins
#define PIN_LCD_G0     8
#define PIN_LCD_G1     20
#define PIN_LCD_G2     3
#define PIN_LCD_G3     46
#define PIN_LCD_G4     9
#define PIN_LCD_G5     10

// Red pins
#define PIN_LCD_R0     4
#define PIN_LCD_R1     5
#define PIN_LCD_R2     6
#define PIN_LCD_R3     7
#define PIN_LCD_R4     15

// Display initialization SPI pins
#define PIN_LCD_CS     39  // Chip Select
#define PIN_LCD_SCLK   48  // Clock
#define PIN_LCD_MOSI   47  // Data

// Backlight control
#define PIN_LCD_BL     38

// Touch screen pins (GT911)
#define PIN_TOUCH_SDA  19
#define PIN_TOUCH_SCL  45
#define PIN_TOUCH_INT  -1  // Not directly connected
#define PIN_TOUCH_RST  -1  // Not directly connected

// SD Card pins
#define PIN_SD_CS      42  // Chip Select
#define PIN_SD_MOSI    47  // Data In (shared with display SPI)
#define PIN_SD_MISO    41  // Data Out
#define PIN_SD_SCK     48  // Clock (shared with display SPI)

// LDR for auto-brightness
#define PIN_LDR        1

// Power control
#define PIN_POWER_ON   -1  // Set to actual pin if available

// I2C bus
#define PIN_I2C_SDA    19
#define PIN_I2C_SCL    45

// Display geometry
#define LCD_WIDTH 480
#define LCD_HEIGHT 480

struct Panel_4848S040_ST7701 : public lgfx::Panel_ST7701_Base
{
    protected:
    const uint8_t* getInitCommands(uint8_t listno) const override
    {
        static constexpr const uint8_t list0[] =
        {
            0xFF,  5, 0x77, 0x01, 0x00, 0x00,
                      0x10,
            0xC0,  2, 0x3B, 0x00,
            0xC1,  2, 0x0D, 0x02,
            0xC2,  2, 0x31, 0x05,
            0xCD,  1, 0x00,
            0xB0, 16, 0x00, 0x11, 0x18, 0x0E,
                      0x11, 0x06, 0x07, 0x08,
                      0x07, 0x22, 0x04, 0x12,
                      0x0F, 0xAA, 0x31, 0x18,
            0xB1, 16, 0x00, 0x11, 0x19, 0x0E,
                      0x12, 0x07, 0x08, 0x08,
                      0x08, 0x22, 0x04, 0x11,
                      0x11, 0xA9, 0x32, 0x18,
            0xFF,  5, 0x77, 0x01, 0x00, 0x00,
                      0x11,
            0xB0,  1, 0x60,
            0xB1,  1, 0x32,
            0xB2,  1, 0x07,
            0xB3,  1, 0x80,
            0xB5,  1, 0x49,
            0xB7,  1, 0x85,
            0xB8,  1, 0x21,
            0xC1,  1, 0x78,
            0xC2,  1, 0x78,
            0xE0,  3, 0x00, 0x1B, 0x02,
            0xE1, 11, 0x08, 0xA0, 0x00, 0x00, 
                      0x07, 0xA0, 0x00, 0x00, 
                      0x00, 0x44, 0x44,
            0xE2, 12, 0x11, 0x11, 0x44, 0x44, 
                      0xED, 0xA0, 0x00, 0x00, 
                      0xEC, 0xA0, 0x00, 0x00,
            0xE3,  4, 0x00, 0x00, 0x11, 0x11,
            0xE4,  2, 0x44, 0x44,
            0xE5, 16, 0x0A, 0xE9, 0xD8, 0xA0, 
                      0x0C, 0xEB, 0xD8, 0xA0,
                      0x0E, 0xED, 0xD8, 0xA0, 
                      0x10, 0xEF, 0xD8, 0xA0,
            0xE6,  4, 0x00, 0x00, 0x11, 0x11,
            0xE7,  2, 0x44, 0x44,
            0xE8, 16, 0x09, 0xE8, 0xD8, 0xA0, 
                      0x0B, 0xEA, 0xD8, 0xA0,
                      0x0D, 0xEC, 0xD8, 0xA0, 
                      0x0F, 0xEE, 0xD8, 0xA0,
            0xEB,  7, 0x02, 0x00, 0xE4, 0xE4, 
                      0x88, 0x00, 0x40,
            0xEC,  2, 0x3C, 0x00,
            0xED, 16, 0xAB, 0x89, 0x76, 0x54, 
                      0x02, 0xFF, 0xFF, 0xFF,
                      0xFF, 0xFF, 0xFF, 0x20, 
                      0x45, 0x67, 0x98, 0xBA,
            0xFF,  5, 0x77, 0x01, 0x00, 0x00, 0x13,
            0xE5,  1, 0xE4,
            0xFF,  5, 0x77, 0x01, 0x00, 0x00, 0x00,
            0x20,  0,
            0x3A,  1, 0x50,
            0x11, CMD_INIT_DELAY, 10,
            0x29, CMD_INIT_DELAY, 120,
            0xFF, 0xFF,
        };
        switch (listno)
        {
            case 0: return list0;
            default: return nullptr;
        }
    }
};

class LGFX : public lgfx::LGFX_Device
{
public:
        lgfx::Bus_RGB _bus_instance;
        Panel_4848S040_ST7701 _panel_instance;

        LGFX(void)
        {
                auto panel_cfg = _panel_instance.config();
                panel_cfg.memory_width = LCD_WIDTH;
                panel_cfg.memory_height = LCD_HEIGHT;
                panel_cfg.panel_width = LCD_WIDTH;
                panel_cfg.panel_height = LCD_HEIGHT;
                panel_cfg.offset_x = 0;
                panel_cfg.offset_y = 0;
                _panel_instance.config(panel_cfg);

                auto cfg_detail = _panel_instance.config_detail();
                cfg_detail.pin_cs = PIN_LCD_CS;
                cfg_detail.pin_sclk = PIN_LCD_SCLK;
                cfg_detail.pin_mosi = PIN_LCD_MOSI;
                _panel_instance.config_detail(cfg_detail);

                auto bus_cfg = _bus_instance.config();
                bus_cfg.panel = &_panel_instance;
                bus_cfg.pin_d0 = PIN_LCD_R0;
                bus_cfg.pin_d1 = PIN_LCD_R1;
                bus_cfg.pin_d2 = PIN_LCD_R2;
                bus_cfg.pin_d3 = PIN_LCD_R3;
                bus_cfg.pin_d4 = PIN_LCD_R4;
                bus_cfg.pin_d5 = PIN_LCD_G0;
                bus_cfg.pin_d6 = PIN_LCD_G1;
                bus_cfg.pin_d7 = PIN_LCD_G2;
                bus_cfg.pin_d8 = PIN_LCD_G3;
                bus_cfg.pin_d9 = PIN_LCD_G4;
                bus_cfg.pin_d10 = PIN_LCD_G5;
                bus_cfg.pin_d11 = PIN_LCD_B0;
                bus_cfg.pin_d12 = PIN_LCD_B1;
                bus_cfg.pin_d13 = PIN_LCD_B2;
                bus_cfg.pin_d14 = PIN_LCD_B3;
                bus_cfg.pin_d15 = PIN_LCD_B4;

                bus_cfg.pin_henable = PIN_LCD_DE;
                bus_cfg.pin_vsync = PIN_LCD_VSYNC;
                bus_cfg.pin_hsync = PIN_LCD_HSYNC;
                bus_cfg.pin_pclk = PIN_LCD_PCLK;

                bus_cfg.freq_write = 14000000;

                bus_cfg.hsync_polarity = 1;
                bus_cfg.hsync_front_porch = 10;
                bus_cfg.hsync_pulse_width = 8;
                bus_cfg.hsync_back_porch = 50;

                bus_cfg.vsync_polarity = 1;
                bus_cfg.vsync_front_porch = 10;
                bus_cfg.vsync_pulse_width = 8;
                bus_cfg.vsync_back_porch = 20;

                bus_cfg.pclk_idle_high = 0;
                bus_cfg.de_idle_high = 1;

                _bus_instance.config(bus_cfg);

                _panel_instance.setBus(&_bus_instance);
                setPanel(&_panel_instance);
        }
};

#endif // ESP32_4848S040_H
