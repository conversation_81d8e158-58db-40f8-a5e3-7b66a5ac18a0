#ifndef INPUT_H
#define INPUT_H

#include "hardware.h"

class Input
{
#if defined(ESP32_4848S040)
    TouchPoint currentTouch = {false, 0, 0};
    GT911 touchScreen;
#elif defined(LILYGO_T_DISPLAY_S3)
    ButtonHandler *button1;
    ButtonHandler *button2;
#endif

public:
    void setup()
    {
#if defined(ESP32_4848S040)
        Serial.println("Initializing touch screen.");
        touchScreen.begin(PIN_I2C_SDA, PIN_I2C_SCL);
#elif defined(LILYGO_T_DISPLAY_S3)
        button1 = new ButtonHandler(PIN_BUTTON_1);
        button2 = new ButtonHandler(PIN_BUTTON_2);
        button1->setOnClickCallback([](int pinNumber, int clickCount)
                                   { Serial.printf("Button on pin %d clicked %d time(s)\n", pinNumber, clickCount); });
        button2->setOnClickCallback([](int pinNumber, int clickCount)
                                   { Serial.printf("Button on pin %d clicked %d time(s)\n", pinNumber, clickCount); });
#endif
    }

    void loop()
    {
#if defined(ESP32_4848S040)
        TouchPoint point = touchScreen.getTouchPoint();
        if (point.touched)
        {
            Serial.print("Touch detected at: ");
            Serial.print(point.x);
            Serial.print(", ");
            Serial.println(point.y);
        }
#endif
    }
};

#endif // INPUT_H