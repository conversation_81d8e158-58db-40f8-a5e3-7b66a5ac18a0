#ifndef THREAD_SAFE_DISPLAY_H
#define THREAD_SAFE_DISPLAY_H

#include <Arduino.h>
#include <LovyanGFX.hpp>
#include <freertos/FreeRTOS.h>
#include <freertos/semphr.h>
#include <vector>
#include "hardware.h"
#include "display-region.h"

/**
 * Manages display regions and prevents overlapping allocations
 */
class DisplayRegionManager
{
public:
    struct Region {
        int32_t x, y, width, height;
        uint8_t id;
        bool active;
        
        Region(int32_t x, int32_t y, int32_t width, int32_t height, uint8_t id) 
            : x(x), y(y), width(width), height(height), id(id), active(true) {}
        
        bool overlaps(int32_t ox, int32_t oy, int32_t ow, int32_t oh) const {
            return !(x >= ox + ow || ox >= x + width || y >= oy + oh || oy >= y + height);
        }
    };

    DisplayRegionManager(int32_t displayWidth, int32_t displayHeight);
    ~DisplayRegionManager();
    
    /**
     * Allocate a new region
     * @param x Left boundary
     * @param y Top boundary  
     * @param width Region width
     * @param height Region height
     * @return Region ID (0 if allocation failed)
     */
    uint8_t allocateRegion(int32_t x, int32_t y, int32_t width, int32_t height);
    
    /**
     * Free a previously allocated region
     * @param regionId ID of the region to free
     */
    void freeRegion(uint8_t regionId);
    
    /**
     * Check if a region allocation would be valid (no overlaps, within bounds)
     */
    bool canAllocateRegion(int32_t x, int32_t y, int32_t width, int32_t height) const;
    
    /**
     * Get region info by ID
     */
    const Region* getRegion(uint8_t regionId) const;
    
    /**
     * Get all active regions
     */
    std::vector<Region> getActiveRegions() const;

private:
    std::vector<Region> _regions;
    int32_t _displayWidth, _displayHeight;
    uint8_t _nextId;
    SemaphoreHandle_t _mutex;
    
    bool isWithinBounds(int32_t x, int32_t y, int32_t width, int32_t height) const;
};

/**
 * Thread-safe wrapper around LGFX that manages display regions
 */
class ThreadSafeDisplay
{
public:
    ThreadSafeDisplay(int32_t displayWidth, int32_t displayHeight);
    ~ThreadSafeDisplay();
    
    /**
     * Initialize the display
     */
    bool setup();
    
    /**
     * Create a new display region for exclusive use by a thread
     * @param x Left boundary of the region
     * @param y Top boundary of the region
     * @param width Width of the region
     * @param height Height of the region
     * @return Pointer to DisplayRegion (nullptr if allocation failed)
     */
    DisplayRegion* createRegion(int32_t x, int32_t y, int32_t width, int32_t height);
    
    /**
     * Release a display region
     * @param region Pointer to the region to release
     */
    void releaseRegion(DisplayRegion* region);
    
    /**
     * Get the underlying LGFX instance (use with caution - not thread safe)
     */
    LGFX& getGfx() { return _gfx; }
    
    /**
     * Get display dimensions
     */
    int32_t getWidth() const { return _displayWidth; }
    int32_t getHeight() const { return _displayHeight; }
    
    /**
     * Perform a full display clear (thread-safe)
     */
    void clearDisplay(uint32_t color = 0x0000);
    
    /**
     * Set display brightness (thread-safe)
     */
    void setBrightness(uint8_t brightness);

private:
    LGFX _gfx;
    DisplayRegionManager _regionManager;
    SemaphoreHandle_t _displayMutex;
    int32_t _displayWidth, _displayHeight;
    std::vector<DisplayRegion*> _activeRegions;
    SemaphoreHandle_t _regionListMutex;
};

#endif // THREAD_SAFE_DISPLAY_H
