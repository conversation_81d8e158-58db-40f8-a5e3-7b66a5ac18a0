#ifndef GT911_H
#define GT911_H

#include <cstdint>

#define GT911_ADDR        0x5D    // Device address when INT is HIGH

// GT911 registers
#define GT911_REG_STATUS  0x814E
#define GT911_POINT1_REG  0x8150

// Touch point structure
struct TouchPoint {
  bool touched;
  uint16_t x;
  uint16_t y;
};

class GT911 {
public:
    void begin(int sda, int scl);
    TouchPoint getTouchPoint();

private:
    TouchPoint readTouchData();
};

#endif // GT911_H