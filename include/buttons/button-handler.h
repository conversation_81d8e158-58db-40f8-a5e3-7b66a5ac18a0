#ifndef BUTTON_HANDLER_H
#define BUTTON_HANDLER_H

#include <ESP32ButtonHandler.h>
#include <functional>

class ButtonHandler : public ESP32ButtonHandler
{
public:
    // Callback function types
    using ClickCallback = std::function<void(int pinNumber, int clickCount)>;
    using PressCallback = std::function<void(int pinNumber)>;

    /**
     * Constructor
     * @param pinNumber Button pin number
     * @param activeLow Whether the button is active low
     * @param pullupActive Whether to enable the internal pullup resistor
     */
    ButtonHandler(int pinNumber, bool activeLow = true, bool pullupActive = true)
        : ESP32ButtonHandler(pinNumber, activeLow, pullupActive), pinNumber(pinNumber)
    {
        // Set up callbacks to forward events to our simplified interface
        ESP32ButtonHandler::setOnClickCallback([this, pinNumber](ESP32ButtonHandler *handler, int clickCount)
                                               {
                if (onClickCallback) {
                    onClickCallback(pinNumber, clickCount);
                } });

        ESP32ButtonHandler::setOnLongPressStartCallback([this, pinNumber](ESP32ButtonHandler *handler)
                                                        {
                if (onLongPressStartCallback) {
                    onLongPressStartCallback(pinNumber);
                } });

        ESP32ButtonHandler::setOnLongPressCallback([this, pinNumber](ESP32ButtonHandler *handler)
                                                   {
                if (onLongPressCallback) {
                    onLongPressCallback(pinNumber);
                } });

        ESP32ButtonHandler::setOnLongPressEndCallback([this, pinNumber](ESP32ButtonHandler *handler)
                                                      {
                if (onLongPressEndCallback) {
                    onLongPressEndCallback(pinNumber);
                } });
    }

    /**
     * Get the pin number for this button handler
     * @return The pin number
     */
    int getPinNumber() const { return pinNumber; }

    /**
     * Set the callback for click events
     * @param callback Function to call when the button is clicked
     */
    void setOnClickCallback(ClickCallback callback) { onClickCallback = callback; }

    /**
     * Set the callback for long press start events
     * @param callback Function to call when a long press starts
     */
    void setOnLongPressStartCallback(PressCallback callback) { onLongPressStartCallback = callback; }

    /**
     * Set the callback for long press events
     * @param callback Function to call during a long press
     */
    void setOnLongPressCallback(PressCallback callback) { onLongPressCallback = callback; }

    /**
     * Set the callback for long press end events
     * @param callback Function to call when a long press ends
     */
    void setOnLongPressEndCallback(PressCallback callback) { onLongPressEndCallback = callback; }

private:
    int pinNumber;
    ClickCallback onClickCallback;
    PressCallback onLongPressStartCallback;
    PressCallback onLongPressCallback;
    PressCallback onLongPressEndCallback;
};

#endif // BUTTON_HANDLER_H