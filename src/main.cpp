#include <Arduino.h>
#include <WiFi.h>

#include "display.h"
#include "system-ui.h"
#include "input.h"
#include "config.h"
#include "wifi-handler.h"

Display *display;
SystemUI *systemUI;
Input *input;
Config *config;
WifiHandler *wifi;

void setup()
{
  Serial.begin(115200);
  delay(2000);

  display = new Display();
  display->setup();

  systemUI = new SystemUI(display);
  systemUI->setup();

  input = new Input();
  input->setup();

  config = new Config();
  const char *ssid = config->getWifiSsid();
  const char *password = config->getWifiPassword();

  if (ssid != NULL && password != NULL)
  {
    wifi = new WifiHandler();
    wifi->setup(ssid, password);
  }
}

void loop()
{
  input->loop();
  systemUI->loop();
}