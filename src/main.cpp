#include <Arduino.h>
#include "display.h"

Display* display;

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== Simple Color Test ===");
    
    display = new Display();
    if (!display->setup()) {
        Serial.println("Failed to init display");
        return;
    }
    
    // Get the LGFX instance from ThreadSafeDisplay
    LGFX& gfx = display->getGfx();
    
    // Test 1: Direct LGFX (should work)
    Serial.println("Drawing direct LGFX red rectangle...");
    gfx.fillRect(10, 10, 100, 100, 0xF800);
    delay(2000);
    
    // Test 2: Get the mutex and try manual locking like DisplayRegion does
    Serial.println("Drawing with manual mutex like DisplayRegion...");
    ThreadSafeDisplay& tsd = display->getThreadSafeDisplay();
    
    // This won't compile because _displayMutex is private
    // Let's try a different approach...
    
    // Test 3: Use DisplayRegion but with a single color
    Serial.println("Testing single DisplayRegion...");
    DisplayRegion* region = display->createRegion(120, 10, 100, 100);
    if (region) {
        Serial.println("Region created, clearing with green...");
        region->clear(0x07E0);  // Green
        Serial.println("Region cleared");
        display->releaseRegion(region);
        Serial.println("Region released");
    }
    
    Serial.println("Test complete - check colors");
}

void loop() {
    delay(1000);
}