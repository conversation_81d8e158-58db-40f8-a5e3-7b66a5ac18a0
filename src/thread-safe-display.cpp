#include "thread-safe-display.h"
#include "hardware.h"

// DisplayRegionManager Implementation
DisplayRegionManager::DisplayRegionManager(int32_t displayWidth, int32_t displayHeight)
    : _displayWidth(displayWidth), _displayHeight(displayHeight), _nextId(1)
{
    _mutex = xSemaphoreCreateMutex();
}

DisplayRegionManager::~DisplayRegionManager()
{
    if (_mutex) {
        vSemaphoreDelete(_mutex);
    }
}

uint8_t DisplayRegionManager::allocateRegion(int32_t x, int32_t y, int32_t width, int32_t height)
{
    if (xSemaphoreTake(_mutex, portMAX_DELAY) != pdTRUE) {
        return 0;
    }
    
    // Check if allocation is valid
    if (!canAllocateRegion(x, y, width, height)) {
        xSemaphoreGive(_mutex);
        return 0;
    }
    
    // Create new region
    uint8_t regionId = _nextId++;
    _regions.emplace_back(x, y, width, height, regionId);
    
    xSemaphoreGive(_mutex);
    return regionId;
}

void DisplayRegionManager::freeRegion(uint8_t regionId)
{
    if (xSemaphoreTake(_mutex, portMAX_DELAY) != pdTRUE) {
        return;
    }
    
    // Find and deactivate the region
    for (auto& region : _regions) {
        if (region.id == regionId) {
            region.active = false;
            break;
        }
    }
    
    xSemaphoreGive(_mutex);
}

bool DisplayRegionManager::canAllocateRegion(int32_t x, int32_t y, int32_t width, int32_t height) const
{
    // Check bounds
    if (!isWithinBounds(x, y, width, height)) {
        return false;
    }
    
    // Check for overlaps with active regions
    for (const auto& region : _regions) {
        if (region.active && region.overlaps(x, y, width, height)) {
            return false;
        }
    }
    
    return true;
}

const DisplayRegionManager::Region* DisplayRegionManager::getRegion(uint8_t regionId) const
{
    for (const auto& region : _regions) {
        if (region.id == regionId && region.active) {
            return &region;
        }
    }
    return nullptr;
}

std::vector<DisplayRegionManager::Region> DisplayRegionManager::getActiveRegions() const
{
    std::vector<Region> activeRegions;
    
    if (xSemaphoreTake(_mutex, portMAX_DELAY) == pdTRUE) {
        for (const auto& region : _regions) {
            if (region.active) {
                activeRegions.push_back(region);
            }
        }
        xSemaphoreGive(_mutex);
    }
    
    return activeRegions;
}

bool DisplayRegionManager::isWithinBounds(int32_t x, int32_t y, int32_t width, int32_t height) const
{
    return (x >= 0 && y >= 0 && 
            x + width <= _displayWidth && 
            y + height <= _displayHeight &&
            width > 0 && height > 0);
}

// ThreadSafeDisplay Implementation
ThreadSafeDisplay::ThreadSafeDisplay(int32_t displayWidth, int32_t displayHeight)
    : _regionManager(displayWidth, displayHeight), _displayWidth(displayWidth), _displayHeight(displayHeight)
{
    _displayMutex = xSemaphoreCreateMutex();
    _regionListMutex = xSemaphoreCreateMutex();
}

ThreadSafeDisplay::~ThreadSafeDisplay()
{
    // Clean up all active regions
    if (xSemaphoreTake(_regionListMutex, portMAX_DELAY) == pdTRUE) {
        for (auto* region : _activeRegions) {
            delete region;
        }
        _activeRegions.clear();
        xSemaphoreGive(_regionListMutex);
    }
    
    if (_displayMutex) {
        vSemaphoreDelete(_displayMutex);
    }
    if (_regionListMutex) {
        vSemaphoreDelete(_regionListMutex);
    }
}

bool ThreadSafeDisplay::setup()
{
    if (xSemaphoreTake(_displayMutex, portMAX_DELAY) != pdTRUE) {
        return false;
    }
    
    // Initialize backlight
    pinMode(PIN_LCD_BL, ANALOG);
    analogWrite(PIN_LCD_BL, 128);
    
    // Initialize display
    bool result = _gfx.init();
    if (result) {
        _gfx.setRotation(0);
        _gfx.fillScreen(TFT_BLACK); // Clear screen to black
    }
    
    xSemaphoreGive(_displayMutex);
    return result;
}

DisplayRegion* ThreadSafeDisplay::createRegion(int32_t x, int32_t y, int32_t width, int32_t height)
{
    // Try to allocate the region
    uint8_t regionId = _regionManager.allocateRegion(x, y, width, height);
    if (regionId == 0) {
        return nullptr; // Allocation failed
    }
    
    // Create the DisplayRegion object
    DisplayRegion* region = new DisplayRegion(_gfx, _displayMutex, x, y, width, height, regionId);
    
    // Add to active regions list
    if (xSemaphoreTake(_regionListMutex, portMAX_DELAY) == pdTRUE) {
        _activeRegions.push_back(region);
        xSemaphoreGive(_regionListMutex);
    }
    
    return region;
}

void ThreadSafeDisplay::releaseRegion(DisplayRegion* region)
{
    if (!region) return;
    
    // Remove from active regions list
    if (xSemaphoreTake(_regionListMutex, portMAX_DELAY) == pdTRUE) {
        auto it = std::find(_activeRegions.begin(), _activeRegions.end(), region);
        if (it != _activeRegions.end()) {
            _activeRegions.erase(it);
        }
        xSemaphoreGive(_regionListMutex);
    }
    
    // Free the region in the manager
    _regionManager.freeRegion(region->getId());
    
    // Delete the region object
    delete region;
}

void ThreadSafeDisplay::clearDisplay(uint32_t color)
{
    if (xSemaphoreTake(_displayMutex, portMAX_DELAY) == pdTRUE) {
        _gfx.fillScreen(color);
        xSemaphoreGive(_displayMutex);
    }
}

void ThreadSafeDisplay::setBrightness(uint8_t brightness)
{
    if (xSemaphoreTake(_displayMutex, portMAX_DELAY) == pdTRUE) {
        analogWrite(PIN_LCD_BL, brightness);
        xSemaphoreGive(_displayMutex);
    }
}
