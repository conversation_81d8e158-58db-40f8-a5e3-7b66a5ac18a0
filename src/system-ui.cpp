#include <Arduino.h>
#include "system-ui.h"
#include <WiFi.h>

SystemUI::SystemUI(Display *display)
    : display(display), lastUpdateTime(0)
{
}

void SystemUI::setup()
{
    // Nothing to do here yet
}

void SystemUI::loop()
{
    unsigned long currentTime = millis();
    if (currentTime - lastUpdateTime > 1000)
    {
        updateSignalStrength();
        lastUpdateTime = currentTime;
    }
}

void SystemUI::updateSignalStrength()
{
    if (WiFi.status() != WL_CONNECTED)
    {
        return;
    }

    int rssi = WiFi.RSSI();
    int strength = map(constrain(rssi, -100, -50), -100, -50, 0, 5);

    // Draw signal strength indicator
    LGFX& gfx = display->getGfx();
    int x = gfx.width() - 30;
    int y = 5;
    int barWidth = 4;
    int barGap = 2;
    int barMaxHeight = 20;

    gfx.fillRect(x, y, (barWidth + barGap) * 5 - barGap, barMaxHeight, TFT_BLACK);

    for (int i = 0; i < 5; i++)
    {
        int barHeight = map(i, 0, 4, 4, barMaxHeight);
        uint16_t color = i < strength ? TFT_BLUE : TFT_SKYBLUE;
        gfx.fillRect(x + i * (barWidth + barGap), y + barMaxHeight - barHeight, barWidth, barHeight, color);
    }
}
