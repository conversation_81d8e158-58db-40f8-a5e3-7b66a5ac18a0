#include <Arduino.h>
#include "system-ui.h"
#include <WiFi.h>

SystemUI::SystemUI(Display *display)
    : display(display), systemRegion(nullptr), lastUpdateTime(0)
{
}

SystemUI::~SystemUI()
{
    if (systemRegion) {
        display->releaseRegion(systemRegion);
        systemRegion = nullptr;
    }
}

void SystemUI::setup()
{
    // Create a region for system UI elements (top-right corner)
    int regionWidth = 50;
    int regionHeight = 30;
    int x = display->getWidth() - regionWidth;
    int y = 0;

    systemRegion = display->createRegion(x, y, regionWidth, regionHeight);

    if (systemRegion) {
        // Clear the region with a dark background
        systemRegion->clear(TFT_BLACK);
    }
}

void SystemUI::loop()
{
    if (!systemRegion) {
        return; // Region not available
    }

    unsigned long currentTime = millis();
    if (currentTime - lastUpdateTime > 1000)
    {
        updateSignalStrength();
        lastUpdateTime = currentTime;
    }
}

void SystemUI::updateSignalStrength()
{
    if (!systemRegion || WiFi.status() != WL_CONNECTED)
    {
        return;
    }

    int rssi = WiFi.RSSI();
    int strength = map(constrain(rssi, -100, -50), -100, -50, 0, 5);

    // Use explicit color values
    const uint16_t COLOR_BLACK = 0x0000;
    const uint16_t COLOR_BLUE = 0x001F;
    const uint16_t COLOR_SKYBLUE = 0x867D;

    // Draw signal strength indicator within our region
    int x = 5; // Relative to region
    int y = 5; // Relative to region
    int barWidth = 4;
    int barGap = 2;
    int barMaxHeight = 20;

    // Clear the signal area first
    systemRegion->fillRect(x, y, (barWidth + barGap) * 5 - barGap, barMaxHeight, COLOR_BLACK);

    // Draw signal bars
    for (int i = 0; i < 5; i++)
    {
        int barHeight = map(i, 0, 4, 4, barMaxHeight);
        uint16_t color = i < strength ? COLOR_BLUE : COLOR_SKYBLUE;
        systemRegion->fillRect(x + i * (barWidth + barGap), y + barMaxHeight - barHeight, barWidth, barHeight, color);
    }
}
