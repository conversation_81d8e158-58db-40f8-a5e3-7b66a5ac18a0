#include <Arduino.h>
#include <LittleFS.h>

#include "config.h"

Config::Config(const char *filename)
{
    this->filename = filename;
    load();
}

void Config::load(void)
{
    if (!LittleFS.begin())
    {
        Serial.println("LittleFS mount failed");
        return;
    }

    File file = LittleFS.open(filename, "r");
    if (file)
    {
        DeserializationError error = deserializeJson(config, file);
        if (error)
        {
            Serial.printf("Failed to read config file [%s]\n", filename);
        }

        file.close();
    }
}

void Config::save(void)
{
    File file = LittleFS.open(filename, "w");
    if (file)
    {
        size_t size = serializeJson(config, file);
        file.close();
    }
}