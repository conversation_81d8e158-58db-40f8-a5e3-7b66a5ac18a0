name=U8g2
version=2.36.7
author=oliver <<EMAIL>>
maintainer=oliver <<EMAIL>>
sentence=Monochrome LCD, OLED and eInk Library. Display controller: SSD1305, SSD1306, SSD1309, SSD1312, <PERSON>D1316, SSD1318, <PERSON>D1320, <PERSON>D1322, <PERSON>D1325, SSD1327, SSD1329, SSD1362, SSD1363, SSD1606, SSD1607, SH1106, SH1107, SH1108, SH1122, T6963, RA8835, LC7981, PCD8544, PCF8812, HX1230, UC1601, UC1604, UC1608, UC1610, UC1611, UC1617, UC1628, UC1638, UC1701, ST7302, ST7511, ST7528, ST7539, ST7565, ST7567, ST7571, ST7586, ST7588, ST75160, ST75161, ST75256, ST75320, NT7534, ST7920, IST3020, IST3088, IST7920, <PERSON>D7032, <PERSON><PERSON>0108, <PERSON><PERSON>0713, <PERSON>44102, T7932, <PERSON><PERSON>1312, <PERSON><PERSON>1330, <PERSON>D1520, <PERSON><PERSON><PERSON>1661, <PERSON>3820, <PERSON><PERSON>7219, <PERSON>1287, GP1247, GP1294, GU800. Interfaces: I2C, SPI, Parallel.
paragraph=Monochrome LCD, OLED and eInk Library. Display controller: SSD1305, SSD1306, SSD1309, SSD1312, SSD1316, SSD1318, SSD1320, SSD1322, SSD1325, SSD1327, SSD1329, SSD1362, SSD1363, SSD1606, SSD1607, SH1106, SH1107, SH1108, SH1122, T6963, RA8835, LC7981, PCD8544, PCF8812, HX1230, UC1601, UC1604, UC1608, UC1610, UC1611, UC1617, UC1628, UC1638, UC1701, ST7302, ST7511, ST7528, ST7539, ST7565, ST7567, ST7571, ST7586, ST7588, ST75160, ST75161, ST75256, ST75320, NT7534, ST7920, IST3020, IST3088, IST7920, LD7032, KS0108, KS0713, HD44102, T7932, SSD1312, SED1330, SED1520, SBN1661, IL3820, MAX7219, GP1287, GP1247, GP1294, GU800. Interfaces: I2C, SPI, Parallel. Features: UTF8, >700 fonts, U8x8 char output.
category=Display
url=https://github.com/olikraus/u8g2
architectures=*
license=BSD-2-Clause
