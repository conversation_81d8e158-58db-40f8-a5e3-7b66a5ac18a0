.pio/build/esp32-4848s040/src/thread-safe-display.cpp.o: \
 src/thread-safe-display.cpp include/thread-safe-display.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp_arduino_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/FreeRTOS.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/FreeRTOSConfig_arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/xtensa_config.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa-versions.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-isa.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/core-matmap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/tie.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/system.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/corebits.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime-frames.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp_rom_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/reset_reasons.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/ets_sys.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_assert.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_bit_defs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/projdefs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/portable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/deprecated_definitions.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portmacro.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/specreg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime-core-state.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xt_instr_macros.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtruntime.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/spinlock.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/cpu_hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_compiler.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/brownout_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/cpu_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/i2c_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/ledc_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/mpu_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/twai_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/cpu_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/esp32s3/include/hal/cpu_ll.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include/xtensa/config/extreg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_attr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/compare_set.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include/soc/soc_memory_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include/esp_private/crosscore_int.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_timer/include/esp_timer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/newlib/platform_include/esp_newlib.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include/esp_heap_caps.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include/multi_heap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include/esp_system.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_idf_version.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_mac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_chip_info.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_random.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portbenchmark.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_api.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include/xtensa/xtensa_context.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include/freertos/portmacro_deprecated.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/mpu_wrappers.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/list.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/task_snapshot.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/semphr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/queue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_sleep.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/touch_sensor_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/gpio_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include/soc/gpio_periph.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/io_mux_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_struct.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_sig_map.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/queue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/event_groups.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos/timers.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/log/include/esp_log.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/log/include/esp_log_internal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-matrix.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-uart.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/uart_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/esp32s3/pins_arduino.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-touch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-dac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/include/driver/gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_intr_alloc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include/esp_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/gpio.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-adc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-spi.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-i2c.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-ledc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rmt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-sigmadelta.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-timer.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-bt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-psram.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-rgb-led.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-cpu.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp8266-compat.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/stdlib_noniso.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/binary.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WCharacter.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/WString.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Printable.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Client.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Server.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Udp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Stream.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/IPAddress.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HardwareSerial.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/HWCDC.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event_base.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event_legacy.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_wifi/include/esp_wifi_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_wifi/include/esp_private/esp_wifi_types_private.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/esp_interface.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include/esp_event_base.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_ip_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_defaults.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_netif_glue.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_com.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/eth_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_mac.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include/esp_eth_phy.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/tcpip_adapter/include/tcpip_adapter.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/tcpip_adapter/include/tcpip_adapter_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/opt.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/lwipopts.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/newlib/platform_include/sys/ioctl.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include/esp_task.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos/FreeRTOSConfig.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/netif/dhcp_state.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/sntp/sntp_get_set_time.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/debug.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/cc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/sys_arch.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch/vfs_lwip.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip4_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip6_addr.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/def.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/ip6_zone.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/include/apps/dhcpserver/dhcpserver.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include/lwip/err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include/esp_netif_sta_list.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/USBCDC.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Esp.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_partition.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_flash.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/spi_flash_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/esp_flash_err.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_spi_flash.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include/esp_spi_flash_counters.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32/spiram.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/io_pin_remap.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Arduino.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/LovyanGFX.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1_init.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/gitTagVersion.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/device.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/Light_PWM.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../../Light.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/Bus_SPI.hpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp32s3/rom/lldesc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/include/driver/spi_common_internal.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/include/driver/spi_common.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include/soc/lldesc.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include/soc/spi_periph.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/periph_defs.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/soc_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/usb_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gpio_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/spi_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/sdio_slave_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/sdmmc_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/touch_sensor_pins.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/spi_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/spi_struct.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/spi_mem_struct.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/spi_mem_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/spi_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_pm/include/esp_pm.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_pm/include/esp32s3/pm.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/rtc.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../../Bus.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../../misc/enum.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../common.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../esp32/common.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../esp32/../../misc/DataWrapper.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../esp32/../../misc/../../utility/pgmspace.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../esp32/../../../utility/result.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../esp32/../../../utility/../internal/memory.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/i2s_reg.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPI/src/SPI.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal-spi.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Wire/src/Wire.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/esp32-hal.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/../../../utility/result.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32/Bus_I2C.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32s3/Bus_Parallel8.hpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/include/esp_lcd_panel_io.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/include/esp_lcd_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/lcd_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/include/esp_private/gdma.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include/soc/gdma_channel.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/gdma_types.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include/hal/dma_types.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32s3/Bus_Parallel16.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/lgfx_filesystem_support.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/../internal/alloca.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/LGFXBase.hpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/Print.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/colortype.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/../../utility/pgmspace.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/pixelcopy.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/lgfx_fonts.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/../utility/pgmspace.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/Touch.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_Device.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/../Panel.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/../boards.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/LGFX_Sprite.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/SpriteBuffer.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/misc/bitmap.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/LGFX_Button.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_GC9A01.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_LCD.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9163.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9225.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9341.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9806.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9342.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ILI948x.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_NT35510.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_R61529.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_RA8875.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_RM68120.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_S6D04K1.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_SSD1306.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_HasBuffer.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/../misc/range.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_SSD1327.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_SSD1331.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_SSD1351.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_SSD1963.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ST7735.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ST7789.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ST7789P3.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ST7796.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_ST77961.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_GDEW0154M09.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_IT8951.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_HUB75.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_FlexibleFrameBuffer.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/../misc/DividedFrameBuffer.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/panel/Panel_M5UnitLCD.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_CST816S.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_FT5x06.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_GSLx680.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/gslx680/Touch_GSLx680_FW.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/gslx680/../../../utility/pgmspace.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_GT911.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_NS2009.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_STMPE610.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_TT21xxx.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_XPT2046.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/touch/Touch_RA8875.hpp \
 include/hardware.h include/boards/esp32-4848s040.h \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32s3/Panel_RGB.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32s3/../../panel/Panel_FrameBufferBase.hpp \
 .pio/libdeps/esp32-4848s040/LovyanGFX/src/lgfx/v1/platforms/esp32s3/Bus_RGB.hpp \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/include/esp_lcd_panel_rgb.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/include/esp_lcd_panel_vendor.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/include/esp_lcd_panel_ops.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/interface/esp_lcd_panel_interface.h \
 include/gt911/gt911.h include/display-region.h include/hardware.h
