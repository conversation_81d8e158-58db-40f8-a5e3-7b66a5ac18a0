{"build_type": "release", "env_name": "lilygo-t-display-s3", "libsource_dirs": ["/Users/<USER>/src/github.com/yawom/esp32-app/lib", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3", "/Users/<USER>/.platformio/lib", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries"], "defines": ["PLATFORMIO=60118", "ARDUINO_LILYGO_T_DISPLAY_S3", "ARDUINO_USB_MODE=1", "ARDUINO_USB_CDC_ON_BOOT=1", "ARDUINO_RUNNING_CORE=1", "ARDUINO_EVENT_RUNNING_CORE=1", "LILYGO_T_DISPLAY_S3", "HAVE_CONFIG_H", "MBEDTLS_CONFIG_FILE=\"mbedtls/esp_config.h\"", "UNITY_INCLUDE_CONFIG_H", "WITH_POSIX", "_GNU_SOURCE", "IDF_VER=\"v4.4.7-dirty\"", "ESP_PLATFORM", "_POSIX_READER_WRITER_LOCKS", "ARDUINO_ARCH_ESP32", "ESP32", "F_CPU=240000000L", "ARDUINO=10812", "ARDUINO_VARIANT=\"lilygo_t_display_s3\"", "ARDUINO_BOARD=\"LilyGo T-Display-S3\"", "ARDUINO_PARTITION_default"], "includes": {"build": ["/Users/<USER>/src/github.com/yawom/esp32-app/include", "/Users/<USER>/src/github.com/yawom/esp32-app/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/LittleFS/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/FS/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/ESP32ButtonHandler/include", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/ESP32ButtonHandler/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/ArduinoJson/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/Callmebot ESP32/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/HTTPClient/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFiClientSecure/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/U8g2/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/LovyanGFX/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Wire/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPI/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/newlib/platform_include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions/freertos", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/port/xtensa/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/esp_additions", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/include/soc/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hw_support/port/esp32s3/private_include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/heap/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/log/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/include/apps", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/include/apps/sntp", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/lwip/src/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/lwip/port/esp32/include/arch", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/soc/esp32s3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/esp32s3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/hal/platform_port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/include/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rom/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_common/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/port/soc", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_system/port/public_compat", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/xtensa/esp32s3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/driver/esp32s3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_pm/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_ringbuf/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/efuse/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/efuse/esp32s3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/vfs/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_wifi/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_event/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_netif/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_eth/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/tcpip_adapter/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_phy/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_phy/esp32s3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_ipc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/app_trace/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_timer/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/mbedtls/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/mbedtls/mbedtls/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/mbedtls/esp_crt_bundle/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/app_update/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spi_flash/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bootloader_support/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/nvs_flash/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/pthread/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_gdbstub/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_gdbstub/xtensa", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_gdbstub/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espcoredump/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espcoredump/include/port/xtensa", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/wpa_supplicant/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/wpa_supplicant/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/wpa_supplicant/esp_supplicant/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/ieee802154/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/console", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/asio/asio/asio/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/asio/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/common/osi/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/include/esp32c3/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/common/api/include/api", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/blufi/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/common/btc/profile/esp/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/host/bluedroid/api/include/api", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_common/tinycrypt/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_core/storage", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/btc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/common/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/client/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/mesh_models/server/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/core/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api/models/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/bt/esp_ble_mesh/api", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/cbor/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/unity/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/unity/unity/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/cmock/CMock/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/coap/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/coap/libcoap/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/nghttp/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/nghttp/nghttp2/lib/includes", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-tls", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-tls/esp-tls-crypto", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_adc_cal/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_hid/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/tcp_transport/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_http_client/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_http_server/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_https_ota/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_https_server/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_lcd/interface", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/protobuf-c/protobuf-c", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/protocomm/include/common", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/protocomm/include/security", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/protocomm/include/transports", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/mdns/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_local_ctrl/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/sdmmc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_serial_slave_link/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_websocket_client/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/expat/expat/expat/lib", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/expat/port/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/wear_levelling/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/fatfs/diskio", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/fatfs/vfs", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/fatfs/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freemodbus/freemodbus/common/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/idf_test/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/idf_test/include/esp32s3", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/jsmn/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/json/cJSON", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/libsodium/libsodium/src/libsodium/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/libsodium/port_include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/mqtt/esp-mqtt/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/openssl/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/perfmon/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/spiffs/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/usb/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/ulp/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/wifi_provisioning/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/rmaker_common/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_diagnostics/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/rtc_store/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_insights/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/json_parser/upstream/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/json_parser/upstream", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/json_generator/upstream", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_schedule/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp_secure_cert_mgr/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_rainmaker/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/gpio_button/button/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/qrcode/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/ws2812_led", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/freertos/include/freertos", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/arduino_tinyusb/tinyusb/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/arduino_tinyusb/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp_littlefs/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/tool", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/typedef", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/image", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/math", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/nn", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/layer", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/detect", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp-dl/include/model_zoo", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp32-camera/driver/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/esp32-camera/conversions/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dotprod/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/support/mem/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/hann/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_harris/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/blackman_nuttall/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/nuttall/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/windows/flat_top/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/iir/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fir/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/add/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sub/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mul/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/addc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/mulc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/math/sqrt/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/add/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/addc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mulc/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/sub/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/fft/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/dct/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/conv/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/common/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/matrix/mul/test/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/espressif__esp-dsp/modules/kalman/ekf_imu13states/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/include/fb_gfx/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/sdk/esp32s3/qio_opi/include", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/variants/lilygo_t_display_s3"], "compatlib": ["/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/ArduinoJson/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/Callmebot ESP32/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/HTTPClient/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFiClientSecure/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/ESP32ButtonHandler/include", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/ESP32ButtonHandler/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/LovyanGFX/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Wire/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPI/src", "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/libdeps/lilygo-t-display-s3/U8g2/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/FS/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/HTTPClient/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/LittleFS/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPI/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFi/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFiClientSecure/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Wire/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/ArduinoOTA/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/AsyncUDP/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/BLE/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/BluetoothSerial/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/DNSServer/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/EEPROM/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/ESP32/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/ESPmDNS/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Ethernet/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/FFat/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/HTTPUpdate/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/HTTPUpdateServer/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/I2S/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Insights/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/NetBIOS/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Preferences/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/RainMaker/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SD/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SD_MMC/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SPIFFS/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/SimpleBLE/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Ticker/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/USB/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/Update/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WebServer/src", "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/libraries/WiFiProv/src"], "toolchain": ["/Users/<USER>/.platformio/packages/toolchain-riscv32-esp@8.4.0+2021r2-patch5/riscv32-esp-elf/include/c++/8.4.0", "/Users/<USER>/.platformio/packages/toolchain-riscv32-esp@8.4.0+2021r2-patch5/riscv32-esp-elf/include/c++/8.4.0/riscv32-esp-elf", "/Users/<USER>/.platformio/packages/toolchain-riscv32-esp@8.4.0+2021r2-patch5/lib/gcc/riscv32-esp-elf/8.4.0/include", "/Users/<USER>/.platformio/packages/toolchain-riscv32-esp@8.4.0+2021r2-patch5/lib/gcc/riscv32-esp-elf/8.4.0/include-fixed", "/Users/<USER>/.platformio/packages/toolchain-riscv32-esp@8.4.0+2021r2-patch5/riscv32-esp-elf/include", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/xtensa-esp32s3-elf/include/c++/8.4.0", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/xtensa-esp32s3-elf/include/c++/8.4.0/xtensa-esp32s3-elf", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/lib/gcc/xtensa-esp32s3-elf/8.4.0/include", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/lib/gcc/xtensa-esp32s3-elf/8.4.0/include-fixed", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/xtensa-esp32s3-elf/include"]}, "cc_flags": ["-std=gnu99", "-Wno-old-style-declaration", "-<PERSON><PERSON>", "-mlongcalls", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-ggdb", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-Wno-error=unused-but-set-variable", "-fno-jump-tables", "-fno-tree-switch-conversion", "-M<PERSON>"], "cxx_flags": ["-std=gnu++11", "-fexceptions", "-fno-rtti", "-<PERSON><PERSON>", "-mlongcalls", "-ffunction-sections", "-fdata-sections", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-error=deprecated-declarations", "-Wno-unused-parameter", "-Wno-sign-compare", "-ggdb", "-freorder-blocks", "-Wwrite-strings", "-fstack-protector", "-fstrict-volatile-bitfields", "-Wno-error=unused-but-set-variable", "-fno-jump-tables", "-fno-tree-switch-conversion", "-M<PERSON>"], "cc_path": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/bin/xtensa-esp32s3-elf-gcc", "cxx_path": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/bin/xtensa-esp32s3-elf-g++", "gdb_path": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp32s3@8.4.0+2021r2-patch5/bin/xtensa-esp32s3-elf-gdb", "prog_path": "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/build/lilygo-t-display-s3/firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "buildfs", "title": "Build Filesystem Image", "description": null, "group": "Platform"}, {"name": "size", "title": "Program Size", "description": "Calculate program size", "group": "Platform"}, {"name": "upload", "title": "Upload", "description": null, "group": "Platform"}, {"name": "uploadfs", "title": "Upload Filesystem Image", "description": null, "group": "Platform"}, {"name": "uploadfsota", "title": "Upload Filesystem Image OTA", "description": null, "group": "Platform"}, {"name": "erase", "title": "Erase Flash", "description": null, "group": "Platform"}], "extra": {"flash_images": [{"offset": "0x0000", "path": "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/build/lilygo-t-display-s3/bootloader.bin"}, {"offset": "0x8000", "path": "/Users/<USER>/src/github.com/yawom/esp32-app/.pio/build/lilygo-t-display-s3/partitions.bin"}, {"offset": "0xe000", "path": "/Users/<USER>/.platformio/packages/framework-arduinoespressif32/tools/partitions/boot_app0.bin"}], "application_offset": "0x10000"}}