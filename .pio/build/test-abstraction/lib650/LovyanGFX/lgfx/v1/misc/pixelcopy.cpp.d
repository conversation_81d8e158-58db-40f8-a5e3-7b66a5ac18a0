.pio/build/test-abstraction/lib650/LovyanGFX/lgfx/v1/misc/pixelcopy.cpp.o: \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/misc/pixelcopy.cpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/misc/pixelcopy.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/misc/colortype.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/misc/../../utility/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/misc/enum.hpp
