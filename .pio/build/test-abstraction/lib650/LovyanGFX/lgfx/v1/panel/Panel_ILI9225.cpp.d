.pio/build/test-abstraction/lib650/LovyanGFX/lgfx/v1/panel/Panel_ILI9225.cpp.o: \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9225.cpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/Panel_ILI9225.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/Panel_LCD.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/Panel_Device.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../Panel.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../../internal/alloca.h \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../misc/enum.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../misc/colortype.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../misc/../../utility/pgmspace.h \
 /Users/<USER>/.platformio/packages/framework-arduinoespressif32/cores/esp32/pgmspace.h \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../misc/pixelcopy.hpp \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../../internal/memory.h \
 .pio/libdeps/test-abstraction/LovyanGFX/src/lgfx/v1/panel/../Bus.hpp
