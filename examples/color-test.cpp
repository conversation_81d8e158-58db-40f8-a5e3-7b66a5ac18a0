/**
 * Color Test Utility for ESP32 RGB Display
 * 
 * This utility helps diagnose color issues by displaying:
 * 1. Primary colors (Red, <PERSON>, Blue)
 * 2. Secondary colors (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>)
 * 3. Grayscale gradient
 * 4. Color values in hex format
 * 
 * Use this to verify that colors are displaying correctly on your RGB display.
 */

#include <Arduino.h>
#include "display.h"
#include "display-region.h"

Display* display;

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("Starting Color Test");
    
    // Initialize display
    display = new Display();
    if (!display->setup()) {
        Serial.println("Failed to initialize display");
        return;
    }
    
    // Clear display to black
    display->clearDisplay(0x0000);
    
    Serial.println("Running color tests...");
    
    // Test 1: Primary Colors
    testPrimaryColors();
    delay(3000);
    
    // Test 2: Secondary Colors  
    testSecondaryColors();
    delay(3000);
    
    // Test 3: Grayscale
    testGrayscale();
    delay(3000);
    
    // Test 4: Color Gradient
    testColorGradient();
    
    Serial.println("Color test complete. Check display for color accuracy.");
}

void testPrimaryColors() {
    Serial.println("Testing primary colors...");
    
    const uint16_t COLOR_RED = 0xF800;      // Pure red
    const uint16_t COLOR_GREEN = 0x07E0;    // Pure green
    const uint16_t COLOR_BLUE = 0x001F;     // Pure blue
    const uint16_t COLOR_WHITE = 0xFFFF;    // White
    
    int regionWidth = display->getWidth() / 3;
    int regionHeight = display->getHeight() / 2;
    
    // Red region
    DisplayRegion* redRegion = display->createRegion(0, 0, regionWidth, regionHeight);
    if (redRegion) {
        redRegion->clear(COLOR_RED);
        redRegion->setCursor(10, 10);
        redRegion->setTextColor(COLOR_WHITE);
        redRegion->setTextSize(2);
        redRegion->print("RED");
        redRegion->setCursor(10, 40);
        redRegion->setTextSize(1);
        redRegion->printf("0x%04X", COLOR_RED);
        redRegion->setCursor(10, 60);
        redRegion->printf("R:%d G:%d B:%d", 
            (COLOR_RED >> 11) & 0x1F,
            (COLOR_RED >> 5) & 0x3F,
            COLOR_RED & 0x1F);
        display->releaseRegion(redRegion);
    }
    
    // Green region
    DisplayRegion* greenRegion = display->createRegion(regionWidth, 0, regionWidth, regionHeight);
    if (greenRegion) {
        greenRegion->clear(COLOR_GREEN);
        greenRegion->setCursor(10, 10);
        greenRegion->setTextColor(COLOR_WHITE);
        greenRegion->setTextSize(2);
        greenRegion->print("GREEN");
        greenRegion->setCursor(10, 40);
        greenRegion->setTextSize(1);
        greenRegion->printf("0x%04X", COLOR_GREEN);
        greenRegion->setCursor(10, 60);
        greenRegion->printf("R:%d G:%d B:%d", 
            (COLOR_GREEN >> 11) & 0x1F,
            (COLOR_GREEN >> 5) & 0x3F,
            COLOR_GREEN & 0x1F);
        display->releaseRegion(greenRegion);
    }
    
    // Blue region
    DisplayRegion* blueRegion = display->createRegion(regionWidth * 2, 0, regionWidth, regionHeight);
    if (blueRegion) {
        blueRegion->clear(COLOR_BLUE);
        blueRegion->setCursor(10, 10);
        blueRegion->setTextColor(COLOR_WHITE);
        blueRegion->setTextSize(2);
        blueRegion->print("BLUE");
        blueRegion->setCursor(10, 40);
        blueRegion->setTextSize(1);
        blueRegion->printf("0x%04X", COLOR_BLUE);
        blueRegion->setCursor(10, 60);
        blueRegion->printf("R:%d G:%d B:%d", 
            (COLOR_BLUE >> 11) & 0x1F,
            (COLOR_BLUE >> 5) & 0x3F,
            COLOR_BLUE & 0x1F);
        display->releaseRegion(blueRegion);
    }
}

void testSecondaryColors() {
    Serial.println("Testing secondary colors...");
    
    const uint16_t COLOR_CYAN = 0x07FF;     // Cyan (Green + Blue)
    const uint16_t COLOR_MAGENTA = 0xF81F;  // Magenta (Red + Blue)
    const uint16_t COLOR_YELLOW = 0xFFE0;   // Yellow (Red + Green)
    const uint16_t COLOR_WHITE = 0xFFFF;
    const uint16_t COLOR_BLACK = 0x0000;
    
    int regionWidth = display->getWidth() / 3;
    int regionHeight = display->getHeight() / 2;
    int yOffset = display->getHeight() / 2;
    
    // Cyan region
    DisplayRegion* cyanRegion = display->createRegion(0, yOffset, regionWidth, regionHeight);
    if (cyanRegion) {
        cyanRegion->clear(COLOR_CYAN);
        cyanRegion->setCursor(10, 10);
        cyanRegion->setTextColor(COLOR_BLACK);
        cyanRegion->setTextSize(2);
        cyanRegion->print("CYAN");
        cyanRegion->setCursor(10, 40);
        cyanRegion->setTextSize(1);
        cyanRegion->printf("0x%04X", COLOR_CYAN);
        display->releaseRegion(cyanRegion);
    }
    
    // Magenta region
    DisplayRegion* magentaRegion = display->createRegion(regionWidth, yOffset, regionWidth, regionHeight);
    if (magentaRegion) {
        magentaRegion->clear(COLOR_MAGENTA);
        magentaRegion->setCursor(10, 10);
        magentaRegion->setTextColor(COLOR_WHITE);
        magentaRegion->setTextSize(2);
        magentaRegion->print("MAGENTA");
        magentaRegion->setCursor(10, 40);
        magentaRegion->setTextSize(1);
        magentaRegion->printf("0x%04X", COLOR_MAGENTA);
        display->releaseRegion(magentaRegion);
    }
    
    // Yellow region
    DisplayRegion* yellowRegion = display->createRegion(regionWidth * 2, yOffset, regionWidth, regionHeight);
    if (yellowRegion) {
        yellowRegion->clear(COLOR_YELLOW);
        yellowRegion->setCursor(10, 10);
        yellowRegion->setTextColor(COLOR_BLACK);
        yellowRegion->setTextSize(2);
        yellowRegion->print("YELLOW");
        yellowRegion->setCursor(10, 40);
        yellowRegion->setTextSize(1);
        yellowRegion->printf("0x%04X", COLOR_YELLOW);
        display->releaseRegion(yellowRegion);
    }
}

void testGrayscale() {
    Serial.println("Testing grayscale...");
    
    display->clearDisplay(0x0000);
    
    DisplayRegion* grayRegion = display->createRegion(0, 0, display->getWidth(), display->getHeight());
    if (grayRegion) {
        // Draw grayscale bars
        int barWidth = display->getWidth() / 16;
        for (int i = 0; i < 16; i++) {
            // Create grayscale color (equal R, G, B values)
            uint8_t grayValue = i * 17; // 0, 17, 34, ..., 255
            uint16_t gray565 = ((grayValue >> 3) << 11) | ((grayValue >> 2) << 5) | (grayValue >> 3);
            
            grayRegion->fillRect(i * barWidth, 0, barWidth, display->getHeight() / 2, gray565);
            
            // Add labels
            grayRegion->setCursor(i * barWidth + 5, display->getHeight() / 2 + 10);
            grayRegion->setTextColor(0xFFFF);
            grayRegion->setTextSize(1);
            grayRegion->printf("%d", grayValue);
        }
        
        display->releaseRegion(grayRegion);
    }
}

void testColorGradient() {
    Serial.println("Testing color gradient...");
    
    display->clearDisplay(0x0000);
    
    DisplayRegion* gradientRegion = display->createRegion(0, 0, display->getWidth(), display->getHeight());
    if (gradientRegion) {
        int width = display->getWidth();
        int height = display->getHeight();
        
        // Create RGB gradient
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                uint8_t r = (x * 255) / width;
                uint8_t g = (y * 255) / height;
                uint8_t b = ((x + y) * 255) / (width + height);
                
                // Convert to RGB565
                uint16_t color = ((r >> 3) << 11) | ((g >> 2) << 5) | (b >> 3);
                gradientRegion->drawPixel(x, y, color);
            }
        }
        
        display->releaseRegion(gradientRegion);
    }
}

void loop() {
    // Nothing to do in loop
    delay(1000);
}
