/**
 * No-Clip Region Test
 * 
 * This test creates a simplified DisplayRegion that doesn't use clipping
 * to see if the clipping logic is causing the color issues.
 */

#include <Arduino.h>
#include "display.h"

Display* display;

// Simplified DisplayRegion without clipping
class SimpleDisplayRegion {
private:
    LGFX& _gfx;
    SemaphoreHandle_t _mutex;
    int32_t _x, _y, _width, _height;
    
    bool acquireLock(TickType_t timeout = portMAX_DELAY) {
        return xSemaphoreTake(_mutex, timeout) == pdTRUE;
    }
    
    void releaseLock() {
        xSemaphoreGive(_mutex);
    }
    
public:
    SimpleDisplayRegion(LGFX& gfx, SemaphoreHandle_t mutex, int32_t x, int32_t y, int32_t width, int32_t height)
        : _gfx(gfx), _mutex(mutex), _x(x), _y(y), _width(width), _height(height) {}
    
    void fillRect(int32_t x, int32_t y, int32_t w, int32_t h, uint32_t color) {
        if (!acquireLock()) return;
        
        // NO CLIPPING - just convert coordinates and draw
        int32_t absX = x + _x;
        int32_t absY = y + _y;
        
        _gfx.fillRect(absX, absY, w, h, color);
        
        releaseLock();
    }
    
    void clear(uint32_t color) {
        fillRect(0, 0, _width, _height, color);
    }
    
    void setCursor(int32_t x, int32_t y) {
        if (!acquireLock()) return;
        
        int32_t absX = x + _x;
        int32_t absY = y + _y;
        
        _gfx.setCursor(absX, absY);
        
        releaseLock();
    }
    
    void setTextColor(uint32_t color) {
        if (!acquireLock()) return;
        
        _gfx.setTextColor(color);
        
        releaseLock();
    }
    
    void setTextSize(float size) {
        if (!acquireLock()) return;
        
        _gfx.setTextSize(size);
        
        releaseLock();
    }
    
    void print(const char* text) {
        if (!acquireLock()) return;
        
        // NO CLIPPING - just print
        _gfx.print(text);
        
        releaseLock();
    }
};

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== No-Clip Region Test ===");
    Serial.println("Testing simplified DisplayRegion without clipping");
    
    // Initialize display
    display = new Display();
    if (!display->setup()) {
        Serial.println("ERROR: Failed to initialize display");
        return;
    }
    
    Serial.println("Display initialized. Running no-clip test...");
    
    // Clear screen to black first
    display->clearDisplay(0x0000);
    delay(1000);
    
    // Get access to the underlying LGFX and mutex
    LGFX& gfx = display->getGfx();
    SemaphoreHandle_t mutex = display->getThreadSafeDisplay()._displayMutex; // This won't work - need to expose it
    
    // For now, let's test without the simplified region and just use direct calls
    testDirectVsRegion();
}

void testDirectVsRegion() {
    LGFX& gfx = display->getGfx();
    
    // Test colors
    const uint16_t RED = 0xF800;
    const uint16_t GREEN = 0x07E0;
    const uint16_t BLUE = 0x001F;
    const uint16_t WHITE = 0xFFFF;
    
    // Test 1: Direct LGFX calls
    Serial.println("\n--- Test 1: Direct LGFX ---");
    gfx.fillRect(10, 10, 80, 80, RED);
    gfx.fillRect(100, 10, 80, 80, GREEN);
    gfx.fillRect(190, 10, 80, 80, BLUE);
    gfx.fillRect(280, 10, 80, 80, WHITE);
    
    gfx.setTextColor(WHITE);
    gfx.setTextSize(1);
    gfx.setCursor(15, 100);
    gfx.print("DIRECT");
    
    Serial.println("Direct LGFX rectangles drawn");
    
    // Test 2: DisplayRegion calls (with clipping)
    Serial.println("\n--- Test 2: DisplayRegion (with clipping) ---");
    
    DisplayRegion* redRegion = display->createRegion(10, 120, 80, 80);
    if (redRegion) {
        redRegion->clear(RED);
        redRegion->setCursor(5, 90);
        redRegion->setTextColor(WHITE);
        redRegion->setTextSize(1);
        redRegion->print("REGION");
        display->releaseRegion(redRegion);
    }
    
    DisplayRegion* greenRegion = display->createRegion(100, 120, 80, 80);
    if (greenRegion) {
        greenRegion->clear(GREEN);
        display->releaseRegion(greenRegion);
    }
    
    DisplayRegion* blueRegion = display->createRegion(190, 120, 80, 80);
    if (blueRegion) {
        blueRegion->clear(BLUE);
        display->releaseRegion(blueRegion);
    }
    
    DisplayRegion* whiteRegion = display->createRegion(280, 120, 80, 80);
    if (whiteRegion) {
        whiteRegion->clear(WHITE);
        display->releaseRegion(whiteRegion);
    }
    
    Serial.println("DisplayRegion rectangles drawn");
    
    // Print comparison
    Serial.println("\n--- Visual Comparison ---");
    Serial.println("Top row: Direct LGFX (should be correct colors)");
    Serial.println("Bottom row: DisplayRegion (may have color issues)");
    Serial.println("Colors should be: RED | GREEN | BLUE | WHITE");
    Serial.println("");
    Serial.println("If bottom row colors are wrong, the issue is in DisplayRegion");
    Serial.println("If both rows are wrong, the issue is in hardware/LGFX setup");
}

void loop() {
    // Print status every 15 seconds
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 15000) {
        Serial.println("No-clip test running. Compare top row (direct) vs bottom row (region)");
        lastPrint = millis();
    }
    
    delay(1000);
}
