/**
 * Direct LGFX Test - Bypass the abstraction to test colors directly
 * 
 * This test bypasses the DisplayRegion abstraction and draws directly
 * to LGFX to see if the color issue is in the abstraction or hardware.
 */

#include <Arduino.h>
#include "display.h"

Display* display;

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== Direct LGFX Color Test ===");
    Serial.println("Testing colors by bypassing DisplayRegion abstraction");
    
    // Initialize display
    display = new Display();
    if (!display->setup()) {
        Serial.println("ERROR: Failed to initialize display");
        return;
    }
    
    Serial.println("Display initialized. Testing direct LGFX access...");
    
    // Get direct access to LGFX
    LGFX& gfx = display->getGfx();
    
    // Clear screen
    gfx.fillScreen(0x0000);
    
    // Test colors directly with LGFX
    testDirectColors(gfx);
    
    Serial.println("Direct LGFX test complete. Check display colors.");
}

void testDirectColors(LGFX& gfx) {
    // Pure RGB565 colors
    const uint16_t PURE_RED = 0xF800;    // 11111 000000 00000
    const uint16_t PURE_GREEN = 0x07E0;  // 00000 111111 00000
    const uint16_t PURE_BLUE = 0x001F;   // 00000 000000 11111
    const uint16_t PURE_WHITE = 0xFFFF;  // 11111 111111 11111
    
    int halfWidth = gfx.width() / 2;
    int halfHeight = gfx.height() / 2;
    
    Serial.printf("Display size: %dx%d\n", gfx.width(), gfx.height());
    Serial.printf("Quadrant size: %dx%d each\n", halfWidth, halfHeight);
    
    // Draw RED quadrant (top-left) - DIRECT LGFX
    Serial.println("Drawing RED quadrant directly...");
    gfx.fillRect(0, 0, halfWidth, halfHeight, PURE_RED);
    gfx.setCursor(20, 50);
    gfx.setTextColor(PURE_WHITE);
    gfx.setTextSize(3);
    gfx.print("RED");
    gfx.setCursor(20, 100);
    gfx.setTextSize(2);
    gfx.printf("0x%04X", PURE_RED);
    gfx.setCursor(20, 130);
    gfx.setTextSize(1);
    gfx.print("DIRECT LGFX");
    
    // Draw GREEN quadrant (top-right) - DIRECT LGFX
    Serial.println("Drawing GREEN quadrant directly...");
    gfx.fillRect(halfWidth, 0, halfWidth, halfHeight, PURE_GREEN);
    gfx.setCursor(halfWidth + 20, 50);
    gfx.setTextColor(0x0000); // Black text on green
    gfx.setTextSize(3);
    gfx.print("GREEN");
    gfx.setCursor(halfWidth + 20, 100);
    gfx.setTextSize(2);
    gfx.printf("0x%04X", PURE_GREEN);
    gfx.setCursor(halfWidth + 20, 130);
    gfx.setTextSize(1);
    gfx.print("DIRECT LGFX");
    
    // Draw BLUE quadrant (bottom-left) - DIRECT LGFX
    Serial.println("Drawing BLUE quadrant directly...");
    gfx.fillRect(0, halfHeight, halfWidth, halfHeight, PURE_BLUE);
    gfx.setCursor(20, halfHeight + 50);
    gfx.setTextColor(PURE_WHITE);
    gfx.setTextSize(3);
    gfx.print("BLUE");
    gfx.setCursor(20, halfHeight + 100);
    gfx.setTextSize(2);
    gfx.printf("0x%04X", PURE_BLUE);
    gfx.setCursor(20, halfHeight + 130);
    gfx.setTextSize(1);
    gfx.print("DIRECT LGFX");
    
    // Draw WHITE quadrant (bottom-right) - DIRECT LGFX
    Serial.println("Drawing WHITE quadrant directly...");
    gfx.fillRect(halfWidth, halfHeight, halfWidth, halfHeight, PURE_WHITE);
    gfx.setCursor(halfWidth + 20, halfHeight + 50);
    gfx.setTextColor(0x0000); // Black text on white
    gfx.setTextSize(3);
    gfx.print("WHITE");
    gfx.setCursor(halfWidth + 20, halfHeight + 100);
    gfx.setTextSize(2);
    gfx.printf("0x%04X", PURE_WHITE);
    gfx.setCursor(halfWidth + 20, halfHeight + 130);
    gfx.setTextSize(1);
    gfx.print("DIRECT LGFX");
    
    Serial.println("All quadrants drawn with direct LGFX calls");
    
    // Print color analysis
    Serial.println("\n=== Color Analysis ===");
    Serial.printf("PURE_RED   = 0x%04X (R:%d G:%d B:%d)\n", 
        PURE_RED, (PURE_RED >> 11) & 0x1F, (PURE_RED >> 5) & 0x3F, PURE_RED & 0x1F);
    Serial.printf("PURE_GREEN = 0x%04X (R:%d G:%d B:%d)\n", 
        PURE_GREEN, (PURE_GREEN >> 11) & 0x1F, (PURE_GREEN >> 5) & 0x3F, PURE_GREEN & 0x1F);
    Serial.printf("PURE_BLUE  = 0x%04X (R:%d G:%d B:%d)\n", 
        PURE_BLUE, (PURE_BLUE >> 11) & 0x1F, (PURE_BLUE >> 5) & 0x3F, PURE_BLUE & 0x1F);
    Serial.printf("PURE_WHITE = 0x%04X (R:%d G:%d B:%d)\n", 
        PURE_WHITE, (PURE_WHITE >> 11) & 0x1F, (PURE_WHITE >> 5) & 0x3F, PURE_WHITE & 0x1F);
}

void loop() {
    // Print status every 10 seconds
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 10000) {
        Serial.println("Direct LGFX test running. Colors should be correct if hardware is OK.");
        Serial.println("Expected: RED (top-left), GREEN (top-right), BLUE (bottom-left), WHITE (bottom-right)");
        lastPrint = millis();
    }
    
    delay(1000);
}
