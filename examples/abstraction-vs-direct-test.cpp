/**
 * Abstraction vs Direct Test
 * 
 * This test compares colors drawn through the DisplayRegion abstraction
 * vs colors drawn directly through LGFX to identify where the color issue occurs.
 */

#include <Arduino.h>
#include "display.h"

Display* display;

void testDirectLGFX() {
    Serial.println("Drawing colors directly with LGFX...");
    
    // Get direct LGFX access
    LGFX& gfx = display->getGfx();
    
    // Clear screen
    gfx.fillScreen(0x0000);
    
    // Define colors
    const uint16_t RED = 0xF800;
    const uint16_t GREEN = 0x07E0;
    const uint16_t BLUE = 0x001F;
    const uint16_t WHITE = 0xFFFF;
    
    int w = gfx.width() / 2;
    int h = gfx.height() / 2;
    
    // Draw quadrants directly
    gfx.fillRect(0, 0, w, h, RED);
    gfx.fillRect(w, 0, w, h, GREEN);
    gfx.fillRect(0, h, w, h, BLUE);
    gfx.fillRect(w, h, w, h, WHITE);
    
    // Add labels
    gfx.setTextSize(2);
    
    gfx.setCursor(10, 20);
    gfx.setTextColor(WHITE);
    gfx.print("DIRECT");
    gfx.setCursor(10, 45);
    gfx.print("RED");
    
    gfx.setCursor(w + 10, 20);
    gfx.setTextColor(0x0000);
    gfx.print("DIRECT");
    gfx.setCursor(w + 10, 45);
    gfx.print("GREEN");
    
    gfx.setCursor(10, h + 20);
    gfx.setTextColor(WHITE);
    gfx.print("DIRECT");
    gfx.setCursor(10, h + 45);
    gfx.print("BLUE");
    
    gfx.setCursor(w + 10, h + 20);
    gfx.setTextColor(0x0000);
    gfx.print("DIRECT");
    gfx.setCursor(w + 10, h + 45);
    gfx.print("WHITE");
    
    Serial.println("Direct LGFX drawing complete");
    Serial.printf("Colors used: RED=0x%04X, GREEN=0x%04X, BLUE=0x%04X, WHITE=0x%04X\n", 
                  RED, GREEN, BLUE, WHITE);
}

void testDisplayRegion() {
    Serial.println("Drawing colors through DisplayRegion abstraction...");
    
    // Clear screen first
    display->clearDisplay(0x0000);
    
    // Define colors (same as direct test)
    const uint16_t RED = 0xF800;
    const uint16_t GREEN = 0x07E0;
    const uint16_t BLUE = 0x001F;
    const uint16_t WHITE = 0xFFFF;
    
    int w = display->getWidth() / 2;
    int h = display->getHeight() / 2;
    
    Serial.printf("Creating regions of size %dx%d\n", w, h);
    
    // RED region (top-left)
    DisplayRegion* redRegion = display->createRegion(0, 0, w, h);
    if (redRegion) {
        Serial.println("Drawing RED region...");
        redRegion->clear(RED);
        redRegion->setTextSize(2);
        redRegion->setCursor(10, 20);
        redRegion->setTextColor(WHITE);
        redRegion->print("REGION");
        redRegion->setCursor(10, 45);
        redRegion->print("RED");
        display->releaseRegion(redRegion);
        Serial.printf("RED region drawn with color 0x%04X\n", RED);
    } else {
        Serial.println("ERROR: Failed to create RED region");
    }
    
    // GREEN region (top-right)
    DisplayRegion* greenRegion = display->createRegion(w, 0, w, h);
    if (greenRegion) {
        Serial.println("Drawing GREEN region...");
        greenRegion->clear(GREEN);
        greenRegion->setTextSize(2);
        greenRegion->setCursor(10, 20);
        greenRegion->setTextColor(0x0000);
        greenRegion->print("REGION");
        greenRegion->setCursor(10, 45);
        greenRegion->print("GREEN");
        display->releaseRegion(greenRegion);
        Serial.printf("GREEN region drawn with color 0x%04X\n", GREEN);
    } else {
        Serial.println("ERROR: Failed to create GREEN region");
    }
    
    // BLUE region (bottom-left)
    DisplayRegion* blueRegion = display->createRegion(0, h, w, h);
    if (blueRegion) {
        Serial.println("Drawing BLUE region...");
        blueRegion->clear(BLUE);
        blueRegion->setTextSize(2);
        blueRegion->setCursor(10, 20);
        blueRegion->setTextColor(WHITE);
        blueRegion->print("REGION");
        blueRegion->setCursor(10, 45);
        blueRegion->print("BLUE");
        display->releaseRegion(blueRegion);
        Serial.printf("BLUE region drawn with color 0x%04X\n", BLUE);
    } else {
        Serial.println("ERROR: Failed to create BLUE region");
    }
    
    // WHITE region (bottom-right)
    DisplayRegion* whiteRegion = display->createRegion(w, h, w, h);
    if (whiteRegion) {
        Serial.println("Drawing WHITE region...");
        whiteRegion->clear(WHITE);
        whiteRegion->setTextSize(2);
        whiteRegion->setCursor(10, 20);
        whiteRegion->setTextColor(0x0000);
        whiteRegion->print("REGION");
        whiteRegion->setCursor(10, 45);
        whiteRegion->print("WHITE");
        display->releaseRegion(whiteRegion);
        Serial.printf("WHITE region drawn with color 0x%04X\n", WHITE);
    } else {
        Serial.println("ERROR: Failed to create WHITE region");
    }
    
    Serial.println("DisplayRegion drawing complete");
}

void loop() {
    // Print status every 15 seconds
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 15000) {
        Serial.println("\n=== Comparison Results ===");
        Serial.println("Check the display:");
        Serial.println("- Top half: Direct LGFX results");
        Serial.println("- Bottom half: DisplayRegion results");
        Serial.println("- Colors should match if abstraction is working correctly");
        lastPrint = millis();
    }
    
    delay(1000);
}

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== Abstraction vs Direct Color Test ===");
    Serial.println("This test compares DisplayRegion vs direct LGFX drawing");
    
    // Initialize display
    display = new Display();
    if (!display->setup()) {
        Serial.println("ERROR: Failed to initialize display");
        return;
    }
    
    Serial.println("Display initialized. Running comparison test...");
    
    // Test 1: Direct LGFX (should work correctly)
    Serial.println("\n--- Test 1: Direct LGFX Drawing ---");
    testDirectLGFX();
    delay(5000);
    
    // Test 2: DisplayRegion abstraction (may have color issues)
    Serial.println("\n--- Test 2: DisplayRegion Abstraction ---");
    testDisplayRegion();
    
    Serial.println("\nTest complete. Compare the two results:");
    Serial.println("- If both look the same, the abstraction is working correctly");
    Serial.println("- If they look different, the abstraction has a color issue");
}
