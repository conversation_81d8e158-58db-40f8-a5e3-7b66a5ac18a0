/**
 * Example demonstrating multi-threaded display usage with DisplayRegion
 * 
 * This example shows how to:
 * 1. Create multiple display regions
 * 2. Assign each region to a different thread
 * 3. Have each thread independently update its region
 * 4. Ensure thread-safe access to the display
 */

#include <Arduino.h>
#include <freertos/FreeRTOS.h>
#include <freertos/task.h>
#include "display.h"
#include "display-region.h"

// Global display instance
Display* display;

// Task handles
TaskHandle_t clockTaskHandle = NULL;
TaskHandle_t statusTaskHandle = NULL;
TaskHandle_t graphTaskHandle = NULL;

/**
 * Clock display task - updates time in top-left region
 */
void clockTask(void* parameter) {
    DisplayRegion* clockRegion = display->createRegion(0, 0, 240, 60);
    
    if (!clockRegion) {
        Serial.println("Failed to create clock region");
        vTaskDelete(NULL);
        return;
    }
    
    // Clear region with dark blue background
    clockRegion->clear(TFT_NAVY);
    
    while (true) {
        // Get current time (simplified - you'd use RTC in real application)
        unsigned long currentTime = millis();
        int seconds = (currentTime / 1000) % 60;
        int minutes = (currentTime / 60000) % 60;
        int hours = (currentTime / 3600000) % 24;
        
        // Clear the text area
        clockRegion->fillRect(10, 10, 220, 40, TFT_NAVY);
        
        // Display time
        clockRegion->setCursor(10, 20);
        clockRegion->setTextColor(TFT_WHITE);
        clockRegion->setTextSize(2);
        clockRegion->printf("%02d:%02d:%02d", hours, minutes, seconds);
        
        vTaskDelay(pdMS_TO_TICKS(1000)); // Update every second
    }
    
    // Clean up (this won't be reached in this example)
    display->releaseRegion(clockRegion);
    vTaskDelete(NULL);
}

/**
 * Status display task - shows system status in top-right region
 */
void statusTask(void* parameter) {
    DisplayRegion* statusRegion = display->createRegion(240, 0, 240, 60);
    
    if (!statusRegion) {
        Serial.println("Failed to create status region");
        vTaskDelete(NULL);
        return;
    }
    
    // Clear region with dark green background
    statusRegion->clear(TFT_DARKGREEN);
    
    int counter = 0;
    
    while (true) {
        // Clear the text area
        statusRegion->fillRect(10, 10, 220, 40, TFT_DARKGREEN);
        
        // Display status info
        statusRegion->setCursor(10, 15);
        statusRegion->setTextColor(TFT_WHITE);
        statusRegion->setTextSize(1);
        statusRegion->printf("Free Heap: %d", ESP.getFreeHeap());
        
        statusRegion->setCursor(10, 35);
        statusRegion->printf("Counter: %d", counter++);
        
        vTaskDelay(pdMS_TO_TICKS(2000)); // Update every 2 seconds
    }
    
    // Clean up (this won't be reached in this example)
    display->releaseRegion(statusRegion);
    vTaskDelete(NULL);
}

/**
 * Graph display task - shows animated graph in bottom region
 */
void graphTask(void* parameter) {
    DisplayRegion* graphRegion = display->createRegion(0, 60, 480, 420);
    
    if (!graphRegion) {
        Serial.println("Failed to create graph region");
        vTaskDelete(NULL);
        return;
    }
    
    // Clear region with black background
    graphRegion->clear(TFT_BLACK);
    
    // Draw graph axes
    graphRegion->drawLine(50, 370, 430, 370, TFT_WHITE); // X-axis
    graphRegion->drawLine(50, 50, 50, 370, TFT_WHITE);   // Y-axis
    
    int dataPoints[50];
    int currentPoint = 0;
    
    while (true) {
        // Generate some sample data (sine wave with noise)
        float angle = (currentPoint * 2 * PI) / 50;
        int value = 160 + (int)(100 * sin(angle)) + random(-20, 20);
        dataPoints[currentPoint] = constrain(value, 60, 360);
        
        // Clear the graph area (keep axes)
        graphRegion->fillRect(51, 51, 379, 318, TFT_BLACK);
        
        // Redraw the data points
        for (int i = 1; i < 50; i++) {
            int idx1 = (currentPoint + i - 1) % 50;
            int idx2 = (currentPoint + i) % 50;
            
            if (dataPoints[idx1] != 0 && dataPoints[idx2] != 0) {
                int x1 = 60 + (i - 1) * 7;
                int y1 = dataPoints[idx1];
                int x2 = 60 + i * 7;
                int y2 = dataPoints[idx2];
                
                graphRegion->drawLine(x1, y1, x2, y2, TFT_CYAN);
                graphRegion->fillCircle(x2, y2, 2, TFT_RED);
            }
        }
        
        currentPoint = (currentPoint + 1) % 50;
        vTaskDelay(pdMS_TO_TICKS(100)); // Update 10 times per second
    }
    
    // Clean up (this won't be reached in this example)
    display->releaseRegion(graphRegion);
    vTaskDelete(NULL);
}

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("Starting Multi-Thread Display Example");
    
    // Initialize display
    display = new Display();
    if (!display->setup()) {
        Serial.println("Failed to initialize display");
        return;
    }
    
    // Clear display
    display->clearDisplay(TFT_BLACK);
    
    Serial.println("Creating tasks...");
    
    // Create tasks for different display regions
    xTaskCreatePinnedToCore(
        clockTask,           // Task function
        "ClockTask",         // Task name
        4096,                // Stack size
        NULL,                // Parameters
        2,                   // Priority
        &clockTaskHandle,    // Task handle
        0                    // Core (0 or 1)
    );
    
    xTaskCreatePinnedToCore(
        statusTask,          // Task function
        "StatusTask",        // Task name
        4096,                // Stack size
        NULL,                // Parameters
        2,                   // Priority
        &statusTaskHandle,   // Task handle
        1                    // Core (0 or 1)
    );
    
    xTaskCreatePinnedToCore(
        graphTask,           // Task function
        "GraphTask",         // Task name
        8192,                // Stack size (larger for graph processing)
        NULL,                // Parameters
        1,                   // Priority (lower than others)
        &graphTaskHandle,    // Task handle
        0                    // Core (0 or 1)
    );
    
    Serial.println("Tasks created successfully");
}

void loop() {
    // Main loop can handle other tasks or just delay
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // Optional: Print task status
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 10000) {
        Serial.printf("Tasks running - Free heap: %d bytes\n", ESP.getFreeHeap());
        lastPrint = millis();
    }
}
