/**
 * Minimal Region Test
 * 
 * This test creates a single DisplayRegion and draws a simple color
 * to isolate the color issue in the abstraction.
 */

#include <Arduino.h>
#include "display.h"

Display* display;

void setup() {
    Serial.begin(115200);
    delay(2000);
    
    Serial.println("=== Minimal Region Test ===");
    Serial.println("Testing single DisplayRegion with simple color");
    
    // Initialize display
    display = new Display();
    if (!display->setup()) {
        Serial.println("ERROR: Failed to initialize display");
        return;
    }
    
    Serial.println("Display initialized. Running minimal test...");
    
    // Clear screen to black first
    display->clearDisplay(0x0000);
    delay(1000);
    
    // Test 1: Direct LGFX red rectangle
    Serial.println("\n--- Test 1: Direct LGFX Red Rectangle ---");
    LGFX& gfx = display->getGfx();
    gfx.fillRect(10, 10, 100, 100, 0xF800); // Pure red
    Serial.println("Direct red rectangle drawn at (10,10) 100x100");
    delay(3000);
    
    // Test 2: DisplayRegion red rectangle (same position, different color for comparison)
    Serial.println("\n--- Test 2: DisplayRegion Green Rectangle ---");
    DisplayRegion* region = display->createRegion(120, 10, 100, 100);
    if (region) {
        Serial.println("Region created successfully");
        region->clear(0x07E0); // Pure green
        Serial.println("Region cleared with green color");
        display->releaseRegion(region);
        Serial.println("Region released");
    } else {
        Serial.println("ERROR: Failed to create region");
    }
    
    // Test 3: DisplayRegion with explicit fillRect
    Serial.println("\n--- Test 3: DisplayRegion Blue fillRect ---");
    DisplayRegion* region2 = display->createRegion(230, 10, 100, 100);
    if (region2) {
        Serial.println("Region2 created successfully");
        region2->fillRect(0, 0, 100, 100, 0x001F); // Pure blue
        Serial.println("Region2 fillRect with blue color");
        display->releaseRegion(region2);
        Serial.println("Region2 released");
    } else {
        Serial.println("ERROR: Failed to create region2");
    }
    
    // Test 4: Check what colors actually appear
    Serial.println("\n--- Visual Check ---");
    Serial.println("You should see:");
    Serial.println("- Left: RED rectangle (direct LGFX)");
    Serial.println("- Middle: GREEN rectangle (DisplayRegion clear)");
    Serial.println("- Right: BLUE rectangle (DisplayRegion fillRect)");
    Serial.println("");
    Serial.println("If colors are wrong, the issue is in DisplayRegion implementation");
}

void loop() {
    // Print status every 10 seconds
    static unsigned long lastPrint = 0;
    if (millis() - lastPrint > 10000) {
        Serial.println("Minimal test running. Check display for three rectangles:");
        Serial.println("RED (direct) | GREEN (region clear) | BLUE (region fillRect)");
        lastPrint = millis();
    }
    
    delay(1000);
}
